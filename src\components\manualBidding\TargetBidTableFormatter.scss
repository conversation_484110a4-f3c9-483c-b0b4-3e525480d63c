#RankMaintenanceList {
  .MuiChip-root {
    width: 100%;
    height: 25px;

    &.MuiChip-outlinedSecondary {
      border: none;
    }

    svg {
      display: none;
      height: 16px;
    }

    span {
      font-family: 'NotoSans';
      font-weight: 500;
      font-size: 13px;
    }

    &.errorStatusLabel-red {
      span {
        font-weight: 700;
        color: var(--error_color);
      }
    }

    &.errorStatusLabel-green {
      span {
        font-weight: 700;
        color: #37a29a;
      }
    }
  }
  .advide-tooltip-header {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
  }
  .no-sorting-advice {
    display: inline-flex;
    align-items: center;
    vertical-align: middle;
  }

  #adviceIcon {
    svg {
      display: flex;
      margin-right: 3px;
      fill: #707070;

      &:hover {
        fill: #66a8e0;
      }
    }
  }

  .MuiSwitch-edgeEnd {
    margin-left: 32% !important;
  }

  .delete-modify-icons {
    svg {
      cursor: pointer;
      display: inline;
    }
  }
  .rankMaintenance-monitoring-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }

  .max-cpc-label {
    font-weight: 700;
    color: var(--error_color);
  }
}

#rank-maintenance-advice-tooltip {
  &-adgroupType, &-maxCpcYn, &-bidYn, &-monitoring, &-report, &-keywordName, &-searchKeyword {
    border: 1px solid var(--point_color);
    background-color: #fff;
    .MuiTooltip-tooltip {
      padding: 0px;
      margin: 0px;
      background-color: transparent;
      .indent1 {
        padding: 0 12px 0 14px;
        text-indent: -14px;
      }
      .indent2 {
        padding: 0 12px 0 30px;
        text-indent: -14px;
      }
      .indent3 {
        padding: 0 12px 0 38px;
        text-indent: -38px;
      }
      > div {
        > div {
          color: var(--point_color);
          font-size: 12px;
          font-weight: 500;
          &:first-child {
            padding: 12px 0px;
            border-bottom: 1px solid #9196a4;
            font-size: 14px;
            font-weight: 700;
            display: flex;
            justify-content: center;
          }
          &:nth-child(2) {
            padding: 12px;

            .bid-desc {
              margin-bottom: 5px;
              line-height: 22px;
            }
            ul {
              margin: 0;
            }
            strong {
              padding:0;
            }
            > div {
              display: flex;
              > div:first-child {
                width: 90px;
                font-weight: 700;
              }
              > div:last-child {
                flex: 1;
                > span {
                  color: var(--error_color);
                }
              }
              .bid-img {
                width: 42px !important;
                height: auto !important;
                margin-right: 10px;
                background-repeat: no-repeat;
                background-size: 100% auto;
                background-position: center center;
              }
              .bid-disable {
                background-image: url(~@images/switch_disable.svg);
              }
              .bid-on {
                background-image: url(~@images/switch_on.svg);
              }
              .bid-off {
                background-image: url(~@images/switch_off.svg);
              }
            }
            // &:not(:first-child) {
            //   margin-top: 10px;
            // }
          }
        }
      }
      span.red {
        color: var(--error_color);
      }
      .MuiTooltip-arrow::before {
        border-color: var(--point_color);
      }
    }
  }
  &-maxCpcYn {
    .MuiTooltip-tooltip > div > div:nth-child(2) > div {
      flex-direction: column;
      margin-bottom: 16px;
    }
  }
}
