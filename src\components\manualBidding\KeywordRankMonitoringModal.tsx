import React, { MouseEvent, ReactElement, useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  IconButton,
  FormLabel,
  Grid,
  TextField,
  MenuItem,
  Box
} from '@material-ui/core'
import { t } from 'i18next'
import ToggleButton from '@material-ui/lab/ToggleButton'
import ToggleButtonGroup from '@material-ui/lab/ToggleButtonGroup'
import KeywordRankMonitoringChart from './KeywordRankMonitoringChart'
import KeywordRankMonitoringTable from './KeywordRankMonitoringTable'
import { DeviceType } from '@models/common/Device'
import { MediaType } from '@models/common/Media'
import { KeywordRankMonitoringViewType } from '@models/rankMaintenance/KeywordRankMonitoring'
import { SSRankMaintenanceKeywordInfo } from '@models/rankMaintenance/RankMaintenance'
import { add, format } from 'date-fns'
import { DateFnsFormat } from '@models/common/CommonConstants'
import { ReactComponent as ChartIcon } from '@components/assets/images/icon_rank_chart.svg'
import { ReactComponent as TableIcon } from '@components/assets/images/icon_rank_table.svg'
import './KeywordRankMonitoringModal.scss'
import { getKeywordRankMonitoringCompetitors } from '@api/manualBidding/RankMonitoring'
import { useSetRecoilState } from 'recoil'
import { SScompetitorsState } from '@store/KeywordRankMonitoring'
import { useToast } from '@hooks/common'
import SelectBottom from '@components/common/SelectBottom'
import { getDeviceCodes } from '@utils/CodeUtil'
import { getTargetDay } from '@utils/RankMaintenanceUtils'
import { object } from 'yup'
import CloseIcon from '@components/assets/images/icon_close.png'
import { ExpandLess } from '@material-ui/icons'
import DatePeriodPickerSearchPage from '@components/common/DatePeriodPickerSearchPage'
import CustomTooltip from '@components/common/CustomTooltip'
import { convertFormatToDate } from '@utils/DateUtil'
import ReportModalTitle from '@components/common/optimization/ReportModalTitle'
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_SA.svg'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
export interface Props {
  onClose: () => void
  adMonitoringId: number
  advertiserName: string
  mediaType: MediaType
  deviceType: DeviceType
  searchKeyword: string
  productTitle: string
  open: boolean
  selectedDate?: string
  adId: string
  keywordList: SSRankMaintenanceKeywordInfo[]
}

interface DatePeriod {
  startDate: string
  endDate: string
}

const KeywordRankMonitoringModal: React.FC<Props> = (props: Props): ReactElement => {
  const minDate = add(new Date(), { years: -2 })
  const today = new Date()
  const [viewType, setViewType] = useState<KeywordRankMonitoringViewType>(KeywordRankMonitoringViewType.GRAPH)
  const [datePeriod, setDatePeriod] = useState<DatePeriod>({
    startDate: getTargetDay(props.selectedDate ?? format(today, DateFnsFormat.DATE), -6).string,
    endDate: props.selectedDate || format(today, DateFnsFormat.DATE)
  })
  const { openToast } = useToast()
  const setCompetitors = useSetRecoilState(SScompetitorsState)
  const [keywordName, setKeywordName] = useState<string>(props.searchKeyword)
  const [adId, setAdId] = useState<string>(props.adId)
  const [adMonitoringId, setAdMonitoringId] = useState<number>(props.adMonitoringId)
  const [deviceType, setDeviceType] = useState<DeviceType>(props.deviceType)
  // FIXME TYPE
  const [keywordList, setKeywordList] = useState<SSRankMaintenanceKeywordInfo[]>(
    props.keywordList.filter((keyword) => keyword.deviceType === props.deviceType)
  )
  const handleChangeViewType = (
    event: MouseEvent<HTMLElement, globalThis.MouseEvent>,
    changedViewType: KeywordRankMonitoringViewType
  ) => {
    if (changedViewType !== null) {
      setViewType(changedViewType)
    }
  }

  const handleChangeDate = async (value: string, changeType: string) => {
    const newDatePeriod = { ...datePeriod, [changeType]: value } as DatePeriod
    validationPeriod
      .validate(newDatePeriod)
      .then((value) => {
        setDatePeriod(newDatePeriod)
      })
      .catch((err) => {
        if (changeType === 'startDate') {
          let endDate = getTargetDay(value, 6).date
          if (endDate > today) {
            endDate = today
          }
          newDatePeriod.endDate = format(endDate, DateFnsFormat.DATE)
        } else {
          newDatePeriod.startDate = getTargetDay(value, -6).string
        }
        setDatePeriod(newDatePeriod)
        openToast(err.message)
      })
  }

  const validationPeriod = object()
    .test('periodInvalid', t('rankMaintenance.message.RankMonitoringModal.validation.periodInvalid'), (value) => {
      return value.startDate <= value.endDate
    })
    .test('periodOverMax', t('rankMaintenance.message.RankMonitoringModal.validation.overPeriodMax'), (value) => {
      const startDate: string = value.startDate
      const endDate: string = value.endDate
      const date = convertFormatToDate(startDate, DateFnsFormat.DATE)
      const aWeekLater = format(date.setDate(date.getDate() + 6), DateFnsFormat.DATE)

      return aWeekLater >= endDate
    })

  const handleSelectedAdId = (event: any) => {
    const keyword = keywordList.find((keyword) => keyword.adId === event.target.value)
    if (keyword) {
      setAdId(event.target.value as string)
      setKeywordName(keyword.searchKeyword)
      setAdMonitoringId(keyword.adMonitoringId)
    }
  }

  const handleChangeDeviceType = (event: any) => {
    const newKeywordList = props.keywordList.filter((keyword) => keyword.deviceType === event.target.value)
    setKeywordList(newKeywordList)
    setDeviceType(event.target.value as DeviceType)
    if (newKeywordList.length) {
      setKeywordName(newKeywordList[0].searchKeyword)
      setAdId(newKeywordList[0].adId)
      setAdMonitoringId(newKeywordList[0].adMonitoringId)
    } else {
      setKeywordName('')
      setAdId('')
      setAdMonitoringId(0)
    }
  }

  const renderDropdownIcon = (props: any) => {
    return (
      <div
        className={`search-input-dropdown ${
          props.className.includes('MuiSelect-iconOpen') ? 'search-input-dropdown-open' : ''
        }`}
      >
        <ExpandLess />
      </div>
    )
  }

  useEffect(() => {
    if (adMonitoringId !== 0) {
      getKeywordRankMonitoringCompetitors(adMonitoringId).then((response) => {
        setCompetitors(response)
      })
    } else {
      setCompetitors([])
    }
  }, [adMonitoringId]) // eslint-disable-line

  return (
    <div>
      <Dialog
        id="SSKeywordRankMonitoringModal"
        open={props.open}
        fullScreen
        scroll="body"
        onClose={() => props.onClose()}
      >
        <DialogTitle>
          <IconButton
            id="MonitoringKeywordAllCloseButton"
            className="modal-close"
            aria-label="close"
            onClick={() => props.onClose()}
          >
            <img alt="close-image" src={CloseIcon} />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Grid container direction="column">
            <Grid item className="title">
              <ReportModalTitle title="Shopping Ad" icon={<TitleLabelIcon />}>
                <span>{t('rankMaintenance.label.RankMonitoringModal.title')}</span>
                <span className="sub-title">{t('rankMaintenance.label.RankMonitoringModal.titleKO')}</span>
              </ReportModalTitle>
            </Grid>
            <Grid container className="search-label-container">
              <Grid item>
                <Box className="shopping-label shopping-advertisement">
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.advertisement')}</FormLabel>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-label shopping-media">
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.media')}</FormLabel>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-label shopping-device">
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.device')}</FormLabel>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-label shopping-adId">
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.adId')}</FormLabel>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-label shopping-keyword">
                  <ModalTooltip id="keyword" field="ranking" color="white" />
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.keyword')}</FormLabel>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-label shopping-period">
                  <ModalTooltip id="period" field="ranking" color="white" />
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.period')}</FormLabel>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-label shopping-view">
                  <ModalTooltip id="view" field="ranking" color="white" />
                  <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.label.view')}</FormLabel>
                </Box>
              </Grid>
            </Grid>
            <Grid container className="search-input-container">
              <Grid item>
                <Box className="shopping-input shopping-advertisement">
                  <CustomTooltip title={props.advertiserName} placement="bottom">
                    <TextField
                      data-testid={'advertiserName'}
                      value={props.advertiserName}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  </CustomTooltip>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-input shopping-media">
                  <CustomTooltip title={props.mediaType} placement="bottom">
                    <TextField
                      data-testid={'mediaType'}
                      value={props.mediaType}
                      InputProps={{
                        readOnly: true
                      }}
                    />
                  </CustomTooltip>
                </Box>
              </Grid>
              <Grid item>
                {/* FIXME */}
                <Box className="shopping-input shopping-device">
                  <SelectBottom
                    data-testid="deviceTypeSelect"
                    displayEmpty
                    onChange={handleChangeDeviceType}
                    value={deviceType}
                    MenuProps={{
                      className: 'keyword-rank-monitoring-filter-popover',
                      anchorOrigin: {
                        vertical: 27,
                        horizontal: 'left'
                      }
                    }}
                    IconComponent={(props) => renderDropdownIcon(props)}
                    renderValue={(value) => {
                      const deviceCode = getDeviceCodes().find((code) => code.deviceType === deviceType)
                      return (
                        <CustomTooltip title={deviceCode?.deviceName || ''} placement="bottom">
                          <span>{deviceCode?.deviceName || ''}</span>
                        </CustomTooltip>
                      )
                    }}
                  >
                    {getDeviceCodes().map((device) => {
                      return (
                        <MenuItem
                          data-testid={`deviceTypeItem-${device.deviceName}`}
                          key={device.deviceType}
                          value={device.deviceType}
                        >
                          {device.deviceName}
                        </MenuItem>
                      )
                    })}
                  </SelectBottom>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-input shopping-adId">
                  <SelectBottom
                    data-testid="keywordSelect"
                    displayEmpty
                    onChange={handleSelectedAdId}
                    value={adId}
                    MenuProps={{
                      className: 'keyword-rank-monitoring-filter-popover',
                      anchorOrigin: {
                        vertical: 27,
                        horizontal: 'left'
                      }
                    }}
                    IconComponent={(props) => renderDropdownIcon(props)}
                    renderValue={(value) => (
                      <CustomTooltip title={value as string} placement="bottom">
                        <span>{value as string}</span>
                      </CustomTooltip>
                    )}
                  >
                    {keywordList.map((keyword) => {
                      return (
                        <MenuItem
                          data-testid={`keywordItem-${keyword.adMonitoringId}`}
                          key={keyword.adMonitoringId}
                          value={keyword.adId}
                        >
                          <CustomTooltip title={keyword.adId} placement="bottom">
                            <span>{keyword.adId}</span>
                          </CustomTooltip>
                        </MenuItem>
                      )
                    })}
                    {keywordList.length < 1 && (
                      <MenuItem value="empty">
                        <span>해당하는 AD ID가 없습니다.</span>
                      </MenuItem>
                    )}
                  </SelectBottom>
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-input shopping-keyword">
                  <TextField
                    data-testid={'advertiserName'}
                    value={keywordName}
                    InputProps={{
                      readOnly: true
                    }}
                  />
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-input shopping-period">
                  <DatePeriodPickerSearchPage
                    id="keyword-rank-monitoring-search-period"
                    disableToolbar={true}
                    startDate={datePeriod.startDate}
                    endDate={datePeriod.endDate}
                    autoOk
                    onClickStartDate={(date) => {
                      date && handleChangeDate(format(date, DateFnsFormat.DATE), 'startDate')
                    }}
                    onClickEndDate={(date) => {
                      date && handleChangeDate(format(date, DateFnsFormat.DATE), 'endDate')
                    }}
                    minStartDate={minDate}
                    maxStartDate={today}
                    minEndDate={minDate}
                    maxEndDate={today}
                  />
                </Box>
              </Grid>
              <Grid item>
                <Box className="shopping-input shopping-view">
                  <ToggleButtonGroup
                    data-testid={'viewTypeSelector'}
                    value={viewType}
                    exclusive
                    onChange={handleChangeViewType}
                  >
                    <ToggleButton data-testid={'viewTypeChartButton'} value={KeywordRankMonitoringViewType.GRAPH}>
                      <ChartIcon className="rank-chart-icon" />
                    </ToggleButton>
                    <ToggleButton data-testid={'viewTypeTableButton'} value={KeywordRankMonitoringViewType.TABLE}>
                      <TableIcon className="rank-table-icon" />
                    </ToggleButton>
                  </ToggleButtonGroup>
                </Box>
              </Grid>
            </Grid>
          </Grid>
          <Grid container justifyContent="flex-end" className="download-button-row"></Grid>
          {viewType === KeywordRankMonitoringViewType.GRAPH && (
            <KeywordRankMonitoringChart
              monitoringId={adMonitoringId}
              startDate={datePeriod.startDate}
              endDate={datePeriod.endDate}
            />
          )}
          {viewType !== KeywordRankMonitoringViewType.GRAPH && (
            <KeywordRankMonitoringTable
              monitoringId={adMonitoringId}
              startDate={datePeriod.startDate}
              endDate={datePeriod.endDate}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default KeywordRankMonitoringModal
