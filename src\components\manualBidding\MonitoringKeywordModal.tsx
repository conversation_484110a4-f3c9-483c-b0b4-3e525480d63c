import React, { useCallback, useEffect, useState } from 'react'
import { getTotalKeywordRanks } from '@api/manualBidding/RankMonitoring'
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  FormLabel,
  Grid,
  IconButton,
  TextField
} from '@material-ui/core'
import { format } from 'date-fns'
import { t } from 'i18next'

import './MonitoringKeywordModal.scss'
import { useRecoilValue } from 'recoil'
import { advertiserState } from '@store/Advertiser'
import { DateFnsFormat } from '@models/common/CommonConstants'
import { ShoppingKeywordRank } from '@models/rankMaintenance/SearchMonitoringKeywordResponse'
import { DataGrid, GridColumnHeaderParams, GridCellParams } from '@material-ui/data-grid'
import {
  Column,
  ColumnCell,
  SSColumnCell,
  HourArray,
  HoursArray
} from '@models/rankMaintenance/MonitoringKeywordAllTable'
import { makeHoursLable } from '@utils/RankMaintenanceUtils'
import { SsRankMonitoringModalParams } from '@models/rankMaintenance/KeywordRankMonitoring'
import CloseIcon from '@components/assets/images/icon_close.png'
import DatePickerSearchPage from '@components/common/DatePicker'
import CustomTooltip from '@components/common/CustomTooltip'
import ReportModalTitle from '@components/common/optimization/ReportModalTitle'
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_SA.svg'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
interface Props {
  onClose: () => void
  onClickKeyword: (onClickParam: SsRankMonitoringModalParams) => void
}

const makeHourlyColumn = (): Column[] => {
  const hours = [...Array(12).keys()]

  return hours.map((hour) => ({
    field: HoursArray[hour],
    width: 62,
    sortable: false,
    align: 'center',
    renderHeader: (params: GridColumnHeaderParams) => {
      // const separator = hour === 0 || hour === 6;
      // return <span className={`${separator ? 'monitoring-keyword-separate' : ''}`}>{makeHoursLable(hour)}</span>;
      return <span>{makeHoursLable(hour)}</span>
    },
    renderCell: (params: GridCellParams) => {
      // const separator = hour === 0 || hour === 6;
      return (
        // <span
        //   className={`${separator ? 'monitoring-keyword-rank monitoring-keyword-separate' : 'monitoring-keyword-rank'}`}
        // >
        <span className="monitoring-keyword-rank">{params.value}</span>
      )
    }
  }))
}

const createData = (data: ShoppingKeywordRank, id: number): SSColumnCell => {
  const result = {} as SSColumnCell

  result.adId = data.adId
  result.mediaType = data.mediaType
  result.media = t(`common.code.media.${data.mediaType}`)
  result.deviceType = data.deviceType
  result.adgroupType = t(`common.code.saShoppingType.${data.adType}`)
  result.searchKeyword = data.searchKeyword ?? ''
  result.productTitle = data.productTitle
  result.adMonitoringId = data.adMonitoringId
  result.id = id

  data.rank.forEach((hourRank, idx) => {
    if (idx % 2 === 0) {
      result[HourArray[idx]] = hourRank === 0 ? null : hourRank
    }
  })

  return result
}

const MonitoringKeywordModal: React.FC<Props> = ({ onClose, onClickKeyword }: Props) => {
  const { advertiserId, advertiserName } = useRecoilValue(advertiserState)
  const [selectedDate, setSelectedDate] = useState<string>(format(new Date(), DateFnsFormat.DATE))
  const [rows, setRows] = useState<SSColumnCell[]>([])
  const headerHeight = 50
  const rowHeight = 55

  const columns: Column[] = [
    {
      field: 'adgroupType',
      headerName: t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.columnHeader.adgroupType'),
      width: 150,
      align: 'center',
      headerAlign: 'center',
      headerClassName: 'adgroupType',
      renderCell: (params: GridCellParams) => {
        return <span className="monitoring-keyword-label">{params.value}</span>
      }
    },
    {
      field: 'deviceType',
      headerName: t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.columnHeader.deviceType'),
      width: 72,
      align: 'center',
      headerAlign: 'center',
      headerClassName: 'deviceName',
      renderCell: (params: GridCellParams) => {
        return <span className="monitoring-keyword-label">{params.value}</span>
      }
    },
    {
      field: 'searchKeyword',
      width: 270,
      align: 'center',
      headerAlign: 'center',
      headerClassName: 'keywordName',
      renderHeader: (params) => {
        return (
          <span className="header-tooltip">
            <ModalTooltip id="keyword" field="ranking" color="white" />
            {t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.columnHeader.keywordName')}
          </span>
        )
      },
      renderCell: ({ row, value }: GridCellParams) => {
        return (
          <CustomTooltip title={value || ''} placement="bottom-end">
            <span
              className="monitoring-keyword-label monitoring-keyword-label-name"
              onClick={() => {
                const { adgroupType, searchKeyword, productTitle, deviceType, mediaType, adMonitoringId, adId } =
                  row as SSColumnCell
                onClickKeyword({
                  adId,
                  adgroupType,
                  searchKeyword,
                  productTitle,
                  mediaType,
                  deviceType,
                  adMonitoringId,
                  selectedDate: selectedDate
                })
              }}
            >
              {value}
            </span>
          </CustomTooltip>
        )
      }
    },
    {
      field: 'adId',
      headerName: t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.columnHeader.adId'),
      width: 270,
      align: 'center',
      headerAlign: 'center',
      headerClassName: 'adId',
      renderCell: (params: GridCellParams) => {
        return <span className="monitoring-keyword-label">{params.value}</span>
      }
    },
    ...makeHourlyColumn()
  ]

  const getData = useCallback(async (advertiserId: number, monitoringDate: string) => {
    const { adRanks } = await getTotalKeywordRanks({ advertiserId, monitoringDate })
    if (adRanks) {
      const convertKeywordToFormat = adRanks.map((keyword, idx) => {
        return createData(keyword, idx)
      })
      setRows(convertKeywordToFormat)
    } else {
      setRows([])
    }
  }, [])

  const changeMonitoringDate = useCallback(async (date: Date | null) => {
    const monitoringDate = format(date as Date, DateFnsFormat.DATE)
    setSelectedDate(monitoringDate)
    await getData(advertiserId, monitoringDate)
  }, [])

  useEffect(() => {
    getData(advertiserId, selectedDate)
  }, []) //eslint-disable-line

  return (
    <Dialog open={true} fullScreen id="RankMaintenanceMonitoringKeywordDialog" scroll="body" onClose={() => onClose()}>
      <DialogTitle>
        <IconButton
          id="MonitoringKeywordAllCloseButton"
          className="modal-close"
          aria-label="close"
          onClick={() => onClose()}
        >
          <img alt="close-image" src={CloseIcon} />
        </IconButton>
      </DialogTitle>
      <DialogContent>
        <Grid container direction="column">
          <Grid item className="title">
            <ReportModalTitle title="Shopping Ad" icon={<TitleLabelIcon />}>
              <span>{t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.title')}</span>
              <span className="sub-title">
                {t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.titleKO')}
              </span>
            </ReportModalTitle>
          </Grid>
        </Grid>
        <Grid container justifyContent="center" className="search-label-container">
          <Grid item>
            <Box className="search-label search-advertiser">
              <FormLabel>{t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.advertiser')}</FormLabel>
            </Box>
          </Grid>
          <Grid item>
            <Box className="search-label search-monitoring-date">
              <FormLabel>{t('rankMaintenance.label.monitoringKeyword.monitoringAllDialog.monitoringDate')}</FormLabel>
            </Box>
          </Grid>
        </Grid>
        <Grid container justifyContent="center" className="search-input-container">
          <Grid item>
            <Box className="search-label search-advertiser">
              <TextField
                className="advertiserField"
                value={advertiserName}
                InputProps={{
                  readOnly: true
                }}
              />
            </Box>
          </Grid>
          <Grid item>
            <Box className="search-label search-monitoring-date">
              <DatePickerSearchPage
                id="rank-maintenance-monitioring-date"
                disableToolbar={true}
                value={selectedDate}
                onClickDate={changeMonitoringDate}
                autoOk
                maxDate={new Date()}
              />
            </Box>
          </Grid>
        </Grid>
        <Grid container justifyContent="flex-end" className="download-button-row">
          {/* NOTE: 추후 다운로드 기능추가예정
          <Grid item>
            <Button className="download-button" disableRipple disabled>
              <DownloadIcon />
            </Button>
          </Grid> */}
        </Grid>
        <div id="DataGridWrapper">
          <DataGrid
            rows={rows}
            columns={columns}
            disableSelectionOnClick
            disableColumnFilter
            disableColumnMenu
            disableExtendRowFullWidth
            pageSize={rows.length}
            hideFooter
            sortingOrder={['asc', 'desc']}
            headerHeight={headerHeight}
            rowHeight={rowHeight}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default MonitoringKeywordModal
