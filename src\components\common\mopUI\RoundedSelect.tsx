import React from 'react'
import SelectBottom from '@components/common/SelectBottom'
import { MenuItem } from '@material-ui/core'
import './RoundedSelect.scss'
import { MopIcon } from '../icon'
import { MOPIcon } from '@models/common'
import { SelectDropdownIcon } from '@components/common/icon'

interface SelectOption {
  label: any
  value: any
  icon?: any
  iconSize?: number
  disabled?: boolean
}
interface Props {
  value: any
  isDisabled?: boolean
  options: SelectOption[]
  placeholder?: string
  updateValue: (value: any) => void
  fontSize?: number
  dropdownSize?: number
}

const RoundedSelect = ({
  isDisabled,
  options,
  placeholder,
  value,
  children,
  fontSize = 12,
  dropdownSize = 24,
  updateValue
}: React.PropsWithChildren<Props>) => {
  const customStyle = {
    '--font-size': `${fontSize}px`,
    '--dropdown-width': `${dropdownSize}px`
  } as React.CSSProperties
  const handleChange = (
    event: React.ChangeEvent<{
      name?: string
      value: any
    }>
  ) => {
    updateValue(event.target.value)
  }

  return (
    <SelectBottom
      className="common-rounded-select"
      style={customStyle}
      value={value}
      onChange={handleChange}
      displayEmpty
      disabled={isDisabled ?? false}
      placeholder={placeholder}
      MenuProps={{ id: 'rounded-select-options', style: customStyle }}
      IconComponent={(props) => <SelectDropdownIcon {...props} size={dropdownSize} />}
    >
      {children}
      {options.map((option, i) => {
        return (
          <MenuItem key={`${option.value}-${i}`} value={option.value} disabled={option.disabled}>
            <div className="rounded-select-item">
              {option.icon && <MopIcon name={MOPIcon[option.icon as keyof typeof MOPIcon]} size={option.iconSize} />}
              <span>{option.label}</span>
            </div>
          </MenuItem>
        )
      })}
    </SelectBottom>
  )
}

export default RoundedSelect
