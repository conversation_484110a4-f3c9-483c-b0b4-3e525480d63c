#RankMaintenanceModal {
  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.4);
  }
  .MuiDialog-scrollBody {
    overflow-x: auto;
  }

  .MuiDialogTitle-root {
    height: 0px;
    padding: 0px;

    .modal-close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      border-radius: 50%;
      background: #fff;
    }
  }

  .MuiDialog-paper {
    width: 1600px;
    max-width: 1600px;
    height: 870px;

    #RankMaintenanceModal-header {
      width: 100%;
      padding: 30px 45px 28px;
      background-color: var(--bg-gray-light);
      .rank-maintenance-target {
        margin-top: 12px;
      }
    }

    #RankMaintenanceModal-body {
      width: 100%;
      height: auto;
      padding: 25px 45px 0px;
      .rank-maintenance-schedule {
        width: 100%;
      }
    }

    .MuiDialogContent-root {
      display: flex;
      flex-flow: column;
      padding: 0;
    }
    .MuiDialogActions-root {
      display: none;
    }

    .rank-maintenance-button-container {
      width: 100%;
      margin-top: 20px;
      display: flex;
      align-items: right;

      .MuiButtonBase-root {
        width: 170px;
        height: 35px;
        margin-left: auto;
        font-size: 17px;
        font-weight: 900;
        color: var(--text-white);
        border-radius: 17.5px;
        background-color: var(--point_color);
        & + .MuiButtonBase-root {
          margin-left: 20px;
        }
      }
    }
  }
  .DialogSaveButton {
    width: 104px;
    height: 26px;
    border-radius: 13px;
    background-color: #016ecb;
    box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
    font-family: 'NotoSans';
    font-weight: 700;
    font-size: 13px;
    color: #ffffff;
  }
}
