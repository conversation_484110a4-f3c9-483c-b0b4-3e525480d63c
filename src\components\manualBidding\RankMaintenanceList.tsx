import { Box, Button, ButtonGroup } from '@material-ui/core'
import React, { useState, ReactElement, useCallback } from 'react'
import { FixedLayoutTable, TablePagination, FixedLayoutColumn } from '@components/common/table'
import { SSRankMaintenanceContextMenuFunctions } from '@models/rankMaintenance/RankMaintenance'
import ruleBasedTableFormatter from './TargetBidTableFormatter'
import { useTranslation } from 'react-i18next'
import { useRecoilValue, useRecoilState, useSetRecoilState } from 'recoil'
import { maintenanceModalState, maintenanceModalType } from '@store/SsRankMaintenance'
import { targetBidListFilterState, targetBidListState } from '@store/SsRankMaintenance'
import AddIcon from '@material-ui/icons/Add'
import '../rankMaintenance/RankMaintenanceList.scss'
import { useAuthority, useToast } from '@hooks/common'
import { ActionType } from '@models/common'
import { ManualBidListItem } from '@models/manualBidding'

const RankMaintenanceList: React.FC = (): ReactElement => {
  const { t } = useTranslation()
  const rmList = useRecoilValue(targetBidListState)
  const { openToast } = useToast()
  const { hasAuthority } = useAuthority()
  const [rmListActiveFilter, setRmListActiveFilter] = useRecoilState(targetBidListFilterState)
  const toggleMaintenanceModal = useSetRecoilState(maintenanceModalState)
  const setModalType = useSetRecoilState(maintenanceModalType)
  const ShoppingBidListTable = new ruleBasedTableFormatter()
  ShoppingBidListTable.handleDelete = (monitoringId: number) => {}
  ShoppingBidListTable.handleEdit = (monitoringId: number) => {}
  const handleChangePage = (newPage: number) => {
    if (newPage !== rmListActiveFilter.pageIndex) {
      setRmListActiveFilter({ ...rmListActiveFilter, pageIndex: newPage })
    }
  }

  const handleChangeRowsPerPage = (newRowsPerPage: number) => {
    if (newRowsPerPage !== rmListActiveFilter.pageSize) {
      setRmListActiveFilter({ ...rmListActiveFilter, pageSize: newRowsPerPage, pageIndex: 1 })
    }
  }

  const handleOrderChange = (orderBy: number, orderDirection: 'asc' | 'desc') => {
    setRmListActiveFilter({
      ...rmListActiveFilter,
      orderBy: 'BID_YN', //FIXME
      sorting: orderDirection.toUpperCase(),
      pageIndex: 1
    })
  }

  const tableColumns: Array<FixedLayoutColumn<ManualBidListItem>> = ShoppingBidListTable.getColumnFormat(
    rmListActiveFilter.orderBy,
    rmListActiveFilter.sorting
  )

  const handleCreate = useCallback(() => {
    if (rmList.ads.length === 10) {
      openToast(`최대 10개까지 설정하실 수 있습니다`)
      return
    }
    setModalType(ActionType.CREATE)
    toggleMaintenanceModal(true)
  }, [])
  return (
    <div id="RankMaintenanceList">
      <Box className="listHeaderWrapper">
        <ButtonGroup>
          {hasAuthority && (
            <Button
              id="createButton"
              data-testid="createButton"
              variant="contained"
              endIcon={<AddIcon />}
              onClick={handleCreate}
            >
              {t('rankMaintenance.label.RankMaintenanceListPage.list.button.new')}
            </Button>
          )}
        </ButtonGroup>
      </Box>
      <FixedLayoutTable
        data-testid="rankMaintenanceTable"
        columns={tableColumns}
        onOrderChange={handleOrderChange}
        data={rmList?.ads?.map((obj: any) => Object.create(obj) || [])}
        localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
      />
      <TablePagination
        id="rank-maintenance-list-pagination"
        totalCount={rmList.totalCount || 0}
        page={rmList.pageIndex || 1}
        rowsPerPage={rmList.pageSize || 10}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        pageOptions={[10, 20, 50]}
      />
    </div>
  )
}
export default RankMaintenanceList
