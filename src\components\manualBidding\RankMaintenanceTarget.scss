#RankMaintenanceTarget {
  .left-grid,
  .right-grid {
    .MuiBox-root {
      display: flex;
      align-items: center;
      & + .MuiBox-root {
        margin-top: 18px;
      }

      .MuiFormLabel-root {
        display: flex;
        align-items: center;
        flex: 1;
        font-size: 19px;
        font-weight: 700;
        color: var(--point_color);

        & > span {
          padding-left: 10px;
          font-size: 13px;
          font-weight: 400;
          color: var(--point_color);
        }
      }
    }

    .MuiInputBase-root {
      width: 300px;
      height: 44px;
      font-size: 17px;
      font-weight: 700;
      color: var(--point_color);
      border: 1px solid #bbbdcd;
      border-radius: 22px;
      background-color: #fff;

      &.MuiInput-underline {
        &::before,
        &::after {
          display: none;
        }
      }

      .MuiSelect-icon {
        top: calc(50% - 12px);
        right: 15px;
        display: inline-block;
        width: 16px;
        height: 16px;
        border-left: 1px solid var(--point_color);
        border-top: 1px solid var(--point_color);
        transform: rotate(-135deg);
        opacity: 0.6;

        &.MuiSelect-iconOpen {
          transform: rotate(45deg);
          top: calc(50% - 3px);
        }

        path {
          display: none;
        }
      }
    }

    .MuiInputBase-input {
      padding: 0 15px;
      font-size: 17px;
      font-weight: 700;
      color: var(--point_color);
      box-sizing: border-box;
      text-align: center;

      &::placeholder {
        font-weight: 400;
        font-size: 17px;
        color: #707070;
        text-align: left;
      }

      &:focus {
        background: none;
      }
      &.Mui-disabled {
        color: rgba(0, 0, 0, 0.38);
      }
    }

    .MuiInputBase-adornedEnd {
      .search-icon,
      .clear-icon {
        width: 21px;
        height: 21px;
        cursor: pointer;
      }
    }
    .MuiOutlinedInput-root,
    .MuiTextField-root {
      .MuiInputBase-input {
        height: 100%;
      }
    }
  }
  .left-grid {
    width: 528px;
    padding-right: 10px;
    .MuiFormLabel-root {
      width: 607px;
    }
  }
  .right-grid {
    flex-grow: 1;
    padding-left: 55px;
    .MuiInputBase-root {
      width: 590px;
    }
  }

  .searchBtn {
    cursor: pointer;
  }
  .clearBtn {
    cursor: pointer;
  }

  #select-adId {
    .MuiOutlinedInput-input {
      padding: 0;
    }
  }
}

.rank-maintenance-advice-tooltip,
#rank-maintenance-target-select-mediaType,
#rank-maintenance-target-select-deviceType,
#rank-maintenance-target-select-keyword {
  .MuiPaper-rounded {
    border-radius: 0px;
    box-shadow: none;
    border: 1px solid #b5b7c9;
  }

  .MuiListItem-button:hover {
    font-weight: 500;
  }

  .MuiMenuItem-root {
    width: 100%;
    height: 100%;
    position: relative;
    font-size: 16px;
    font-weight: 400;
    color: var(--point_color);
    text-align: center;
    border-radius: 0px;
    &.Mui-selected {
      font-weight: 700;
      background-color: transparent;
    }
  }
}
#rank-maintenance-target-select-mediaType,
#rank-maintenance-target-select-deviceType {
  .MuiMenuItem-root {
    margin: 8px 0;
    padding: 8px 0;
  }
}
#rank-maintenance-target-select-keyword {
  .mop-search-wrapper {
    width: 90%;
    margin: 0 auto;
    margin-bottom: 8px;
  }
  & ul {
    max-height: 420px;
  }
  .initial-select-value {
    display: none !important;
  }
  .keyword-search-items {
    margin: 0;
    padding: 8px 0;
    & > .MuiListItemText-root {
      padding: 0 !important;
      margin: 0 !important;
    }
  }
}
