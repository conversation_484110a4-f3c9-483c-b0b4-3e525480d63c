#KeywordCompetitorSettingModal {
  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.4);
  }

  .MuiDialogTitle-root {
    height: 0px;
    padding: 0px;

    .modal-close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      border-radius: 50%;
      background: #f3f3f6;
    }
  }

  .MuiDialog-paper {
    width: 1000px;
    height: auto;
    box-sizing: border-box;

    .MuiDialogContent-root {
      padding: 29px 28px 18px;

      .competitor-table {
        width: 100%;
        margin: 20px 0;

        border-top: 2px solid var(--point_color);
        border-bottom: 2px solid var(--point_color);

        .MuiTableContainer-root {
          max-height: 503px;
          height: 503px;
          &::-webkit-scrollbar-thumb {
            background-color: var(--point_color);
          }
          &::-webkit-scrollbar-track {
            background-color: transparent;
          }
        }

        .MuiTableCell-root {
          height: 41px;
          padding: 0px;
          font-size: 15px;
          font-weight: 500;
          color: var(--point_color);
          text-align: center;
          border-bottom: none;
        }

        .MuiTableHead-root {
          .MuiTableCell-head {
            height: 40px;
            font-size: 18px;
            font-weight: 500;
            background-color: var(--bg-table-sub);
            border-bottom: 2px solid var(--border-table-sub);
          }
        }

        .MuiButtonBase-root {
          width: 10px;
          height: 10px;
          .MuiIconButton-label {
            svg {
              display: none;
            }
            &::before {
              content: '';
              width: 19px;
              height: 19px;
              position: absolute;
              top: 0;
              left: 0;
              border: 1px solid #dedfe4;
              background-color: #fff;
              box-sizing: content-box;
            }
          }
          &.Mui-checked .MuiIconButton-label::after {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 15px;
            height: 15px;
            background-color: var(--status-active);
          }
        }
      }

      .buttonWrapper {
        display: flex;
        justify-content: center;
        margin-top: 18px;

        #addButton {
          border-radius: 0;
          width: 108px;
          height: 30px;
          padding-left: 12px;
          position: relative;
          font-size: 14px;
          font-weight: 400;
          color: var(--point_color);
          text-align: left;
          border: 1px solid #bbbdcd;

          &::after {
            content: '';
            width: 15px;
            height: 16px;
            position: absolute;
            top: 50%;
            right: 12px;
            transform: translateY(-50%);
          }
        }
      }
      .saveButtonWrapper {
        width: 100%;
        display: flex;
        .DialogSaveButton {
          width: 170px;
          height: 35px;
          margin-left: auto;
          font-size: 17px;
          font-weight: 900;
          color: var(--text-white);
          border-radius: 17.5px;
          background-color: var(--point_color);
        }
      }
    }

    .MuiDialogActions-root {
      padding: 0;
      padding-top: 15px;

      .DialogSaveButton {
        width: 170px;
        height: 35px;
        margin-left: auto;
        font-size: 17px;
        font-weight: 900;
        color: var(--text-white);
        border-radius: 17.5px;
        background-color: var(--point_color);
      }
    }
  }

  .MuiTableBody-root {
    overflow-y: auto;
    overflow-x: hidden;
    .MuiTableCell-body {
      border-top: 1px solid #f1f1f1;
    }
  }

  .MuiTable-root {
    border-collapse: separate;
    table-layout: fixed;

    .competitor-table-column {
      &-ss-checkbox {
        width: 30px;
      }
      &-product {
        width: 377px;
      }
      &-alias {
        width: 120px;
      }
    }
    .competitor-table-body-cell {
      &-product {
        .competitor-product {
          display: flex;
          align-items: center;
          // justify-content: center;
          &-image {
            width: 75px;
            height: 75px;
            margin: 8px;
          }
          &-detail {
            flex: 1;
            display: flex;
            flex-direction: column;
            .product-title {
              font-size: 16px;
              font-weight: 700;
              text-align: left;
              color: var(--point_color);
            }
            .product-info {
              display: flex;
              align-items: center;
              justify-content: space-between;
              &-box {
                flex: 1;
                text-align: left;
              }
              &-label {
                color: #909090;
                font-size: 12px;
                font-weight: 300;
                margin-right: 8px;
                vertical-align: middle;
              }
              &-value {
                color: var(--point_color);
                font-size: 16px;
                &.price {
                  font-weight: 700;
                }
              }
            }
          }
        }
      }
    }

    .competitor-table-body-cell-alias {
      padding: 0 10px;
      .MuiInputBase-root {
        width: 100%;
        height: 100%;
        padding-right: 7px;
        font-size: 15px;
        font-weight: 300;
        color: var(--point_color);

        fieldset {
          border: none;
        }

        &.MuiInput-underline {
          &::before,
          &::after {
            display: none;
          }
        }

        .MuiInputBase-input {
          width: 100%;
          height: 100%;
          padding: 0 10px;
          text-align: center;
          font-size: 16px;
          font-weight: 300;
          color: var(--point_color);
          box-sizing: border-box;

          &:focus {
            background: none;
          }
        }

        input {
          padding: 1px 15px 1px 15px;
        }

        &.MuiOutlinedInput-adornedStart {
          padding-left: 0px;
        }

        &.MuiInputBase-adornedEnd .search-icon {
          width: 26px;
          height: 26px;
          cursor: pointer;
        }
      }
    }
  }
}
