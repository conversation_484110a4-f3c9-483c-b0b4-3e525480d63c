import { ReactElement } from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { FixedLayoutColumn } from '@components/common/table'
import { BudgetOptimizationListItem } from '@models/budgetOpt/BudgetOpt'
import CommonTooltip from '@components/common/CommonTooltip'
import CustomTooltip from '@components/common/CustomTooltip'
import TooltipCard from '@components/common/tooltip/TooltipCard'
import { withStyles } from '@material-ui/core/styles'
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import { ColumnSettingButtons } from '@components/common/buttons'
import { BudgetOptimizationStatus, BudgetOptimizationPerformImpact } from '@models/budgetOpt/BudgetOpt'

import { ReactComponent as PerformImpactHigh } from '@components/assets/images/performImpactHigh.svg'
import { ReactComponent as PerformImpactLow } from '@components/assets/images/performImpactLow.svg'
import { ReactComponent as PerformImpactNormal } from '@components/assets/images/performImpactNormal.svg'
import { ReactComponent as ResultIcon } from '@components/assets/images/icon_optimization_result.svg'
import { ReactComponent as CopyIcon } from '@components/assets/images/icon_copy.svg'

import './BudgetOptTableFormatter.scss'
import { useAuthority } from '@hooks/common'
import TagManager from 'react-gtm-module'

type BudgetOptimizationTableRow = FixedLayoutColumn<BudgetOptimizationListItem>

interface TitleTooltipProps {
  tooltipTitle: any
  label: string | ReactElement
}

const TitleTooltip = ({ tooltipTitle, label }: TitleTooltipProps) => {
  return (
    <div className="tooltip-container">
      <CommonTooltip title={tooltipTitle} placement="bottom-start" arrow>
        <span>
          <AdviceMarkIcon className="tooltip-icon" /> {label}
        </span>
      </CommonTooltip>
    </div>
  )
}

const ImpactTooltip = withStyles({
  tooltip: {
    '& > div': {
      display: 'flex',
      alignItems: 'center',
      gap: 6,
      '& svg': {
        backgroundColor: '#fff',
        padding: 2,
        borderRadius: 2,
        width: 30,
        height: 30
      },
      '& span': {
        fontSize: 14
      },
      '& small': {
        fontSize: 10
      }
    }
  }
})(CustomTooltip)

const ImpactLevelIcon = ({ level }: { level: string }) => {
  return (
    <>
      {BudgetOptimizationPerformImpact.HIGH === level && <PerformImpactHigh />}
      {BudgetOptimizationPerformImpact.NORMAL === level && <PerformImpactNormal />}
      {BudgetOptimizationPerformImpact.LOW === level && <PerformImpactLow />}
    </>
  )
}

export default class BudgetOptTableFormatter {
  handleDelete(id: number) {}
  handleCopy(id: number) {}
  getColumnFormat = (): Array<BudgetOptimizationTableRow> => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { advertiser, hasAuthority } = useAuthority()
    const getImpactLevel = (changeRate: number, improveRate: number) => {
      changeRate = changeRate * 0.01
      const lowRange = 0 < improveRate && improveRate <= 0.5 * changeRate
      const normalRange = 0.5 * changeRate < improveRate && improveRate <= changeRate
      const highRange = changeRate < improveRate
      if (lowRange) return BudgetOptimizationPerformImpact.LOW
      if (normalRange) return BudgetOptimizationPerformImpact.NORMAL
      if (highRange) return BudgetOptimizationPerformImpact.HIGH
      return ''
    }

    const columnOptStatus = () => {
      return {
        title: (
          <CommonTooltip
            placement="bottom-start"
            arrow
            title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.status'} type={'defineList'} />}
          >
            <span>
              <AdviceMarkIcon className="tooltip-icon" /> {t('optimization.label.list.status')}
            </span>
          </CommonTooltip>
        ),
        field: 'status',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120
        },
        render: ({ status }) => {
          return (
            <span className={`status ${status}`}>
              <div>{t(`optimization.label.budgetOpt.list.status.${status}`)}</div>
            </span>
          )
        }
      } as BudgetOptimizationTableRow
    }

    const columnOptId = () => {
      return {
        title: (
          <CommonTooltip
            placement="bottom-start"
            arrow
            title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.optimizationId'} type={'paragraph'} />}
          >
            <span>
              <AdviceMarkIcon className="tooltip-icon" /> {t(`optimization.label.list.optimizationId`)}
            </span>
          </CommonTooltip>
        ),
        field: 'optimizationId',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120
        },
        render: (rowData) => {
          const canCopy = BudgetOptimizationStatus.SETTING !== rowData.status && hasAuthority
          return (
            <div className="cell-body-box">
              {rowData.optimizationId}
              {canCopy && (
                <CopyIcon
                  onClick={(e) => {
                    this.handleCopy(rowData.optimizationId)

                    const gtmEventId = e.currentTarget.dataset.gtmId
                    if (gtmEventId) {
                      TagManager.dataLayer({
                        dataLayer: {
                          event: 'click',
                          gtm_id: gtmEventId
                        }
                      })
                    }
                  }}
                  data-gtm-id="budget-opt-copy-click"
                />
              )}
            </div>
          )
        }
      } as BudgetOptimizationTableRow
    }

    const columnOptName = () => {
      return {
        title: t(`optimization.label.list.optimizationName`),
        field: 'optimizationName',
        align: 'center',
        sorting: false,
        cellStyle: {
          minWidth: 240,
          cursor: 'pointer'
        },
        render: (rowData) => {
          return (
            <div
              className="cell-body-box"
              onClick={(e) => {
                navigate(`./${rowData.optimizationId}`, { state: { advertiserId: advertiser.advertiserId } })

                const gtmEventId = e.currentTarget.dataset.gtmId
                if (gtmEventId) {
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'click',
                      gtm_id: gtmEventId
                    }
                  })
                }
              }}
              data-gtm-id="budget-opt-detail"
            >
              {rowData.optimizationName}
            </div>
          )
        }
      } as BudgetOptimizationTableRow
    }

    const columnBudgetVariance = () => {
      return {
        title: t(`optimization.label.list.budgetVariance`),
        field: 'budgetChangeRate',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120
        },
        render: (rowData) => {
          if (!rowData.budgetChangeRate) return <></>
          return <span>{`${rowData.budgetChangeRate}% ↓`}</span>
        }
      } as BudgetOptimizationTableRow
    }

    const columnOptDate = () => {
      return {
        title: (
          <CommonTooltip
            placement="bottom-start"
            arrow
            title={<TooltipCard tKey={'optimization.label.budgetOpt.tooltip.engineRunDate'} type={'paragraph'} />}
          >
            <span>
              <AdviceMarkIcon className="tooltip-icon" /> {t(`optimization.label.list.optDate`)}
            </span>
          </CommonTooltip>
        ),
        field: 'engineRunDate',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120
        },
        render: (rowData) => {
          if (rowData.engineRunDate && BudgetOptimizationStatus.FINISHED === rowData.status) {
            return (
              <div className="cell-body-box">
                <span>{rowData.engineRunDate}</span>
                <ResultIcon
                  onClick={(e) => {
                    navigate(`./${rowData.optimizationId}?step=3`)

                    const gtmEventId = e.currentTarget.dataset.gtmId
                    if (gtmEventId) {
                      TagManager.dataLayer({
                        dataLayer: {
                          event: 'click',
                          gtm_id: gtmEventId
                        }
                      })
                    }
                  }}
                  data-gtm-id="budget-opt-date-click"
                />
              </div>
            )
          }
          return <></>
        }
      } as BudgetOptimizationTableRow
    }

    const columnPerformanceForedcast = () => {
      return {
        title: (
          <TitleTooltip
            label={<>{t('optimization.label.list.estimatedImpact')}</>}
            tooltipTitle={
              <>
                <h1>{t('optimization.label.budgetOpt.tooltip.performImproveRate.title')}</h1>
                <div className="common-style">
                  <p>{t('optimization.label.budgetOpt.tooltip.performImproveRate.contents.0')}</p>
                  <dl className="w12_w88">
                    <dt>
                      <ImpactLevelIcon level={'HIGH'} /> :
                    </dt>
                    <dd>{t('optimization.label.budgetOpt.tooltip.performImproveRate.contents.1')}</dd>
                    <dt>
                      <ImpactLevelIcon level={'NORMAL'} /> :
                    </dt>
                    <dd>{t('optimization.label.budgetOpt.tooltip.performImproveRate.contents.2')}</dd>
                    <dt>
                      <ImpactLevelIcon level={'LOW'} /> :
                    </dt>
                    <dd>{t('optimization.label.budgetOpt.tooltip.performImproveRate.contents.3')}</dd>
                  </dl>
                </div>
              </>
            }
          />
        ),
        field: 'performImproveRate',
        align: 'center',
        sorting: false,
        cellStyle: {
          width: '10%',
          minWidth: 120
        },
        render: ({ budgetChangeRate, performImproveRate, optimizationId }) => {
          if (!performImproveRate) return <></>
          const impactLevel = getImpactLevel(budgetChangeRate, performImproveRate)
          return (
            <ImpactTooltip
              title={
                <div>
                  <ImpactLevelIcon level={impactLevel} />
                  {impactLevel === 'HIGH' && (
                    <div>
                      <span>Highly Recommend</span>
                      <br />
                      <small>예산 변경 적극 추천</small>
                    </div>
                  )}
                  {impactLevel === 'NORMAL' && (
                    <div>
                      <span>Recommend</span>
                      <br />
                      <small>예산 변경 추천</small>
                    </div>
                  )}
                  {impactLevel === 'LOW' && (
                    <div>
                      <span>Go over</span>
                      <br />
                      <small>예산 변경 영향 발생</small>
                    </div>
                  )}
                </div>
              }
              placement="bottom"
            >
              <div
                className="cell-body-box"
                onClick={(e) => {
                  navigate(`./${optimizationId}?step=3`)

                  const gtmEventId = e.currentTarget.dataset.gtmId
                  if (gtmEventId) {
                    TagManager.dataLayer({
                      dataLayer: {
                        event: 'click',
                        gtm_id: gtmEventId
                      }
                    })
                  }
                }}
                data-gtm-id="budget-opt-perform-improve-click"
              >
                <span>
                  Avg. <strong>{(performImproveRate * 100).toFixed(1)}</strong>% ↑
                </span>
                <ImpactLevelIcon level={impactLevel} />
              </div>
            </ImpactTooltip>
          )
        }
      } as BudgetOptimizationTableRow
    }

    const getColumnContextMenu = () => {
      return {
        field: 'CONTEXT_MENU',
        sorting: false,
        cellStyle: {
          width: '10%'
        },
        render: (rowData) => {
          if (rowData.status === BudgetOptimizationStatus.RUNNING || !hasAuthority) return <></>
          return (
            <ColumnSettingButtons
              handleEdit={() => {
                navigate(`./${rowData.optimizationId}?step=1`, { state: { advertiserId: advertiser.advertiserId } })
                TagManager.dataLayer({
                  dataLayer: {
                    event: 'click',
                    gtm_id: 'budget-opt-modify',
                    status: rowData.status,
                    optimization_id: rowData.optimizationId,
                    budget_change_rate: rowData.budgetChangeRate,
                    perform_improve_rate: rowData.performImproveRate
                  }
                })
              }}
              editGtmId="budget-opt-modify"
              handleDelete={() => {
                this.handleDelete(rowData.optimizationId)
                TagManager.dataLayer({
                  dataLayer: {
                    event: 'click',
                    gtm_id: 'budget-opt-delete',
                    status: rowData.status,
                    optimization_id: rowData.optimizationId,
                    budget_change_rate: rowData.budgetChangeRate,
                    perform_improve_rate: rowData.performImproveRate
                  }
                })
              }}
              deleteGtmId="budget-opt-delete"
            />
          )
        }
      } as BudgetOptimizationTableRow
    }

    return [
      columnOptStatus(),
      columnOptId(),
      columnOptName(),
      columnBudgetVariance(),
      columnOptDate(),
      columnPerformanceForedcast(),
      getColumnContextMenu()
    ]
  }
}
