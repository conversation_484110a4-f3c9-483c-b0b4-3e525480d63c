// import React from 'react'
import { t } from 'i18next'
import Switch from '@material-ui/core/Switch'

import { RankMaintenanceListColumn } from '@models/rankMaintenance/RankMaintenance'
import { ManualBidListItem } from '@models/manualBidding'
import { AuthorityType } from '@models/common/Advertiser'
import { checkAuthority } from '@utils/AuthorityUtil'
import './TargetBidTableFormatter.scss'
import { FixedLayoutColumn } from '@components/common/table'
import CustomTooltip from '@components/common/CustomTooltip'
import {
  Tooltip,
  columnBidYnProps,
  columnMediaTypeProps,
  columnAdgroupProps,
  columnCampaignProps,
  columnProps,
  tooltipColumnProps
} from '@components/common/rankMaintenance/ColumnProps'
import { useAuthority, useToast } from '@hooks/common'

import { EllipsisText, MopIcon } from '@components/common'
import { MOPIcon, YNFlag } from '@models/common'

export default class TargetBiddingListTable {
  constructor() {
    this.sorting = 'asc'
    this.orderBy = ''
  }
  sorting: 'asc' | 'desc'
  orderBy: string
  handleView(_id: number) {}
  handleEdit(_id: number) {}
  handleDelete(_id: number) {}
  updateBidYn(_id: number, _checked: boolean) {}
  getColumnFormat = (
    orderBy?: string | undefined,
    sorting?: string | undefined
  ): Array<FixedLayoutColumn<ManualBidListItem>> => {
    const { advertiser } = useAuthority()
    const { openToast } = useToast()

    const getColumnBidYn = () => {
      return {
        ...columnBidYnProps('ss'),
        title: (
          <span className="advide-tooltip-header">
            <Tooltip id="bidYn" listType="ss" />
            {t(`rankMaintenance.shoppingBid.label.bidYn`)}
          </span>
        ),
        render: (rowData) => {
          return (
            <div id={`bidYn-${rowData.adId}`} className="bidYn">
              <Switch
                data-testid={`bidYnSwitch-${rowData.adId}`}
                className="bidYnSwitch"
                edge="end"
                checked={rowData.bidYn === YNFlag.Y}
                onChange={(_evt, checked) => this.updateBidYn(rowData.adMonitoringId, checked)}
                disabled={!checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)}
              />
            </div>
          )
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnMediaType = () => {
      return {
        ...columnMediaTypeProps(),
        defaultSort: orderBy === RankMaintenanceListColumn.mediaType ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.mediaType !== null) {
            return (
              <div onClick={() => this.handleView(rowData.adMonitoringId)}>
                <span data-testid={`mediaType-${rowData.adId}`}>{t(`common.code.media.${rowData.mediaType}`)}</span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnAdName = () => {
      return {
        ...columnProps('productName', true, 150),
        defaultSort: orderBy === RankMaintenanceListColumn.productName ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.productName !== null) {
            return (
              <div onClick={() => this.handleView(rowData.adMonitoringId)}>
                <CustomTooltip title={rowData.adId} placement="bottom">
                  <EllipsisText data-testid={`adName-${rowData.adId}`}>{rowData.productName}</EllipsisText>
                </CustomTooltip>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnAdId = () => {
      return {
        ...columnProps('adId', true, 150),
        title: (
          <span className="advide-tooltip-header">
            <Tooltip id="adId" listType="ss" />
            {t(`rankMaintenance.label.RankMaintenanceListPage.list.adId`)}
          </span>
        ),
        defaultSort: orderBy === RankMaintenanceListColumn.adId ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.adId !== null) {
            return (
              <div onClick={() => this.handleView(rowData.adMonitoringId)}>
                <span data-testid={`adId-${rowData.adId}`} className="overflow-line">
                  {rowData.adId}
                </span>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnCampaignName = () => {
      return {
        ...columnCampaignProps(),
        defaultSort: orderBy === RankMaintenanceListColumn.campaignName ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.campaignName !== null) {
            return (
              <div>
                <CustomTooltip
                  data-testid={`campaignNameTooltip-${rowData.adId}`}
                  title={rowData.campaignName || ''}
                  placement="bottom"
                  onClick={() => this.handleView(rowData.adMonitoringId)}
                >
                  <span data-testid={`campaignName-${rowData.adId}`} className="overflow-line">
                    {rowData.campaignName}
                  </span>
                </CustomTooltip>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnAdgroupName = () => {
      return {
        ...columnProps('adgroupName', true, 180),
        defaultSort: orderBy === RankMaintenanceListColumn.adgroupName ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.adgroupName !== null) {
            return (
              <div>
                <CustomTooltip
                  title={rowData.adgroupName}
                  placement="bottom"
                  onClick={() => this.handleView(rowData.adMonitoringId)}
                >
                  <span data-testid={`adgroupName-${rowData.adId}`} className="overflow-line">
                    {rowData.adgroupName}
                  </span>
                </CustomTooltip>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getBidKeyword = () => {
      return {
        ...columnProps('searchKeyword', true, 120, 'rankMaintenance.shoppingBid.label'),
        title: (
          <span className="advide-tooltip-header">
            <Tooltip id="searchKeyword" listType="ss" />
            {t(`rankMaintenance.shoppingBid.label.searchKeyword`)}
          </span>
        ),
        defaultSort: orderBy === RankMaintenanceListColumn.searchKeyword ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.searchKeyword !== null) {
            return (
              <div>
                <CustomTooltip
                  title={rowData.searchKeyword}
                  placement="bottom"
                  onClick={() => this.handleView(rowData.adMonitoringId)}
                >
                  <span data-testid={`searchKeyword-${rowData.adId}`} className="overflow-line">
                    {rowData.searchKeyword}
                  </span>
                </CustomTooltip>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }
    const getBidCriteria = () => {
      return {
        ...columnProps('bidCriteria', true, 70, 'rankMaintenance.shoppingBid.label'),
        defaultSort: orderBy === RankMaintenanceListColumn.bidCriteria ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          if (rowData.bidCriteria !== null) {
            return (
              <div>
                <CustomTooltip
                  title={rowData.bidCriteria}
                  placement="bottom"
                  onClick={() => this.handleView(rowData.adMonitoringId)}
                >
                  <span data-testid={`bidCriteria-${rowData.adId}`} className="overflow-line">
                    {t(`rankMaintenance.label.RankMaintenanceListPage.status.${rowData.bidCriteria}`)}
                  </span>
                </CustomTooltip>
              </div>
            )
          }
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnMaxCpcYn = () => {
      return {
        ...tooltipColumnProps('maxCpcYn', true, 90),
        title: (
          <span className="advide-tooltip-header">
            <Tooltip id="maxCpcYn" listType="ss" />
            {t(`rankMaintenance.label.RankMaintenanceListPage.list.maxCpcYn`)}
          </span>
        ),
        defaultSort: orderBy === RankMaintenanceListColumn.maxCpcYn ? sorting?.toLowerCase() : undefined,
        render: (rowData) => {
          return (
            <>
              {rowData.maxCpcYn === 'Y' && (
                <span className="max-cpc-label">
                  {t('rankMaintenance.label.RankMaintenanceListPage.status.MAX_CPC')}
                </span>
              )}
            </>
          )
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const getColumnContextMenu = () => {
      return {
        title: '수정/삭제',
        field: RankMaintenanceListColumn.contextMenu,
        sorting: false,
        cellStyle: {
          width: 130
        },
        render: (rowData) => {
          return (
            <div className="delete-modify-icons">
              <MopIcon
                name={MOPIcon.EDIT}
                data-testid={`edit-${rowData.adId}`}
                onClick={() => {
                  if (rowData.bidYn === YNFlag.Y) {
                    openToast(t('rankMaintenance.label.RankMaintenanceListPage.nonEditable'))
                    return
                  }

                  this.handleEdit(rowData.adMonitoringId)
                }}
              />
              <MopIcon
                name={MOPIcon.DELETE}
                data-testid={`delete-${rowData.adId}`}
                onClick={(e) => {
                  if (rowData.bidYn === YNFlag.Y) {
                    openToast(t('rankMaintenance.label.RankMaintenanceListPage.impossibleDelete'))
                    return
                  }
                  this.handleDelete(rowData.adMonitoringId)
                }}
              />
            </div>
          )
        }
      } as FixedLayoutColumn<ManualBidListItem>
    }

    const columns: FixedLayoutColumn<ManualBidListItem>[] = [
      getColumnBidYn(),
      getColumnAdName(), // ad name
      getColumnMediaType(),
      getColumnAdId(),
      getColumnCampaignName(),
      getColumnAdgroupName(),
      getBidKeyword(),
      getBidCriteria(),
      getColumnMaxCpcYn()
    ]

    if (checkAuthority(AuthorityType.OPERATE, advertiser.authorityType)) {
      columns.push(getColumnContextMenu())
    }

    return columns
  }
}
