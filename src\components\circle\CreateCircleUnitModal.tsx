import React, { ReactElement } from 'react'
import { useTranslation } from 'react-i18next';
import { Dialog } from '@material-ui/core'
import { MopActionButton } from '@components/common/buttons'
import { MopIcon } from '@components/common'
import { MOPIcon, YNFlag } from '@models/common'
import { CreaeteUnitForm, CreateCircleUnit } from '@models/circle'
import { FixedLayoutTable } from '@components/common/table'
import { CreateUnitListTableFormatter } from '@components/circle'
import circleApi from '@api/circle'
import { useToast } from "@hooks/common"
import './CreateCircleUnitModal.scss'

export interface Props {
  onClose: any
  open: boolean
  callback: () => void
  createInfo: CreaeteUnitForm
  advertiserId: string
}

const CreateCircleUnitModal: React.FC<Props> = ({
  open, onClose, createInfo, advertiserId, callback
}: Props): ReactElement => {
  const { t } = useTranslation();
  const { units } = createInfo
  const table = new CreateUnitListTableFormatter(createInfo)
  const allColumns = table.getColumnFormat()
  const { openToast } = useToast()

  const createCircleUnit = async () => {
    const createUnits = units.reduce((result: CreateCircleUnit[], curr) => {
      result.push({
        analyticsMetric: createInfo.analyticsMetric,
        analyticsType: createInfo.analyticsType,
        mediaAccountId: createInfo.mediaAccountId,
        mediaType: createInfo.mediaType,
        campaignId: curr.campaignId,
        ...( createInfo.analyticsType !== 'MEDIA' && {
          analyticsAccountId: createInfo.analyticsAccountId,
          analyticsSubId: createInfo.analyticsSubId
        })
      })
      return result
    }, [])
    const result = await circleApi.createCircleUnit(advertiserId, createUnits)
    if (result.successOrNot === YNFlag.Y) {
      openToast('신규 유닛을 등록했습니다.')
      callback()
    } else {
      openToast('신규 유닛 등록에 실패하였습니다. 시스템 담당자에게 문의주시기 바랍니다.')
    }
    onClose()
  }
  return (
    <Dialog className="create-circle-unit-modal" open={open} onClose={onClose}>
      <section className="create-circle-unit-modal__header">
        <MopIcon name={MOPIcon.CLOSE} onClick={onClose} bgColor="white" />
        <span>아래 유닛을 <strong>추가</strong>하시겠습니까?</span>
      </section>
      <section className="create-circle-unit-modal__content">
        <FixedLayoutTable
          tableType="list-table"
          columns={allColumns}
          data={units}
          localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
        />
      </section>
      <section className="create-circle-unit-modal__footer">
        <MopActionButton label="취소" theme="cancel" onClick={onClose} />
        <MopActionButton label="추가" onClick={createCircleUnit} />
      </section>
    </Dialog>
  )
}

export default CreateCircleUnitModal