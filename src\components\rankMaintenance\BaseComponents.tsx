import tw from 'twin.macro'

// common form field
export const FormField = tw.div`grid grid-cols-1 [&:has(>*:nth-child(2):last-child)]:grid-cols-[2fr_3fr] items-center justify-between`
export const FormLabel = tw.label`flex items-center gap-2.5 text-lg font-bold text-primary-black`
export const FormSubLabel = tw.span`text-xs font-normal`
export const ReadOnlyField = tw.div`w-full min-w-[300px] h-[44px] leading-[44px] border border-input-outline rounded-full bg-white text-center text-primary-black font-bold text-lg px-4`
