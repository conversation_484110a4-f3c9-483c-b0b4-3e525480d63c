import { But<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogTitle, Grid, OutlinedInput } from '@material-ui/core'
import React, { ReactElement, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useTextCheck } from '@hooks/common'
import { Close } from '@material-ui/icons'
import './CreateBudgetOptModal.scss'
import { ActionType } from '@models/common/CommonConstants'
import { createBudgetOptimization, updateBudgetOptimization } from '@api/budgetOpt/BudgetOpt'
import { KpiItem } from '@models/budgetOpt/BudgetOpt'
import { DvKpiType } from '@models/optimization/Kpi'
import { useActionType, useToast } from '@hooks/common'
import { YNFlag } from '@models/common/YNFlag'
interface Props {
  advertiserId: number
  open: boolean
  onClose: () => void
  successAction?: () => void
  modalType?: ActionType
  existingOptName?: string
  existingKpis?: KpiItem[]
  optimizationId?: number
}

const CreateBudgetOptModal: React.FC<Props> = ({
  open,
  onClose,
  successAction,
  advertiserId,
  modalType = ActionType.CREATE,
  existingOptName,
  existingKpis,
  optimizationId
}: Props): ReactElement => {
  const { canCreate, isEditType } = useActionType(modalType)
  const { openToast } = useToast()
  const { isEmptyText, isSameText, toastExceedMsg } = useTextCheck()
  const { t } = useTranslation()
  const [optimizationName, setOptimizationName] = useState<string>('')
  const [isDisabled, setIsDisabled] = useState(true)

  const createBudgetOpt = async (advertiserId: number, optimizationName: string) => {
    const result = await createBudgetOptimization({ advertiserId, optimizationName })
    openToast(t(`optimization.label.budgetOpt.list.create.${result ? 'success' : 'failed'}`))
  }
  const editBudgetOpt = async (optimizationId: number, optimizationName: string) => {
    const defaultKpi: KpiItem | null =
      existingKpis && existingKpis.length > 0 ? null : { kpiType: DvKpiType.IMPRESSIONS, kpiValue: -1 }
    const result = await updateBudgetOptimization(optimizationId, {
      optimizationName,
      triggerEngine: YNFlag.N,
      ...(defaultKpi ? { kpis: [defaultKpi] } : {})
    })
    openToast(t(`optimization.label.budgetOpt.editOpt.${result ? 'success' : 'failed'}`))
  }

  const handleSave = async () => {
    try {
      if (canCreate) await createBudgetOpt(advertiserId, optimizationName)
      if (isEditType && optimizationId) await editBudgetOpt(optimizationId, optimizationName)
      successAction && successAction()
    } catch (error) {
      openToast(t(`optimization.label.budgetOpt.${isEditType ? 'editOpt' : 'create'}.failed`))
    } finally {
      handleModalClose()
    }
  }
  const handleChange = ({ target: { value: textVal } }: React.ChangeEvent<HTMLInputElement>) => {
    const isEmpty = isEmptyText(textVal)
    const isExceedingText = toastExceedMsg(textVal, '최적화 명')
    setOptimizationName(isExceedingText ? textVal.slice(0, 100) : textVal)
    if (isEmpty !== isDisabled) setIsDisabled(isEmpty)
    if (isSameText(textVal, existingOptName)) setIsDisabled(true)
  }
  const handleModalClose = () => {
    setOptimizationName('')
    setIsDisabled(true)
    onClose()
  }

  useEffect(() => {
    if (existingOptName && existingOptName.trim().length > 0 && open) {
      setOptimizationName(existingOptName)
      setIsDisabled(false)
    }
  }, [open])

  return (
    <Dialog open={open} id="create-budget-opt-modal" onClose={handleModalClose}>
      <DialogTitle className="modal-title-container">
        <p className="modal-title">최적화명</p>
        <button className="modal-close-button" onClick={handleModalClose}>
          <Close />
        </button>
      </DialogTitle>
      <DialogContent>
        <Grid container className="modal-content-form">
          <Grid className="grid-row">
            <OutlinedInput
              className="modal-content-input"
              value={optimizationName}
              type="text"
              onChange={handleChange}
              onKeyUp={(e) => e.key === 'Enter' && handleSave()}
              placeholder={'Enter Optimization Name'}
            />
          </Grid>
          <Grid className="grid-row">
            <Button className="modal-save-button" variant="contained" onClick={handleSave} disabled={isDisabled}>
              {t('setting.member.list.label.save')}
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  )
}

export default CreateBudgetOptModal
