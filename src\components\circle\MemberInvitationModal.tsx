import React, { type ReactElement, useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { getReceiveInvitation, replyInvitation } from '@api/circle'
import type { SessionInfo } from '@models/common/Session'
import type { ReceiveInvitationItem } from '@models/circle'
import { Dialog, DialogContent } from '@material-ui/core'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { TextIcon } from '@components/common/icon'
import { MopIcon, EllipsisText } from '@components/common'
import { MOPIcon, YNFlag } from '@models/common'
import { useToast } from '@hooks/common'
import SessionUtil from '@utils/SessionUtil'

import './MemberInvitationModal.scss'
import { MemberStatusCode } from '@models/member/Member'

export interface Props {
  advertiser: { advertiserId: number; advertiserName: string }
  member: SessionInfo
  onClose: any
  open: boolean
}

interface DecisionButtonProps {
  decision: string
}

const DecisionButton = styled.button<DecisionButtonProps>`
  ${tw`py-1.5 px-5 rounded font-bold text-xs`};
  ${({ decision }) => {
    switch (decision) {
      case 'accept':
        return tw`bg-[#E8F2FE] text-[#3B5EC9]`
      case 'reject':
        return tw`bg-[#FFE1E3] text-[#EB424C]`
      default:
        return tw`bg-[#E8F2FE] text-[#3B5EC9]`
    }
  }}
`

const MemberInvitationModal: React.FC<Props> = (props: Props): ReactElement => {
  const { t } = useTranslation()
  const { openToast } = useToast()
  const [invitationList, setInvitationList] = useState<ReceiveInvitationItem[] | undefined>(undefined)
  const sessionUtil = new SessionUtil()

  const handleClose = () => {
    props.onClose()
  }

  const handleReply = async (approveYn: string, invitationId: number) => {
    const result = await replyInvitation({ approveYn, invitationId })
    if (result.successOrNot === YNFlag.Y) {
      if (approveYn === YNFlag.Y) {
        openToast('초대를 수락하였습니다.')
        if (sessionUtil.getSessionInfo().memberStatusCode === MemberStatusCode.INITIAL) {
          const newSessionInfo = { ...sessionUtil.getSessionInfo(), memberStatusCode: result.data.memberStatusCode }
          sessionUtil.setSessionInfo(newSessionInfo)
        }
      } else {
        openToast('초대를 거절하였습니다.')
      }
    } else {
      openToast('응답에 실패했습니다. 다시 시도해주세요.')
    }
    await getInvitationList()
  }

  const getInvitationList = async (): Promise<ReceiveInvitationItem[]> => {
    try {
      const data: ReceiveInvitationItem[] = (await getReceiveInvitation()) || []
      setInvitationList(data)
      return data
    } catch (error) {
      console.error('Failed to fetch invitation items:', error)
      return []
    }
  }

  useEffect(() => {
    if (props.open) {
      getInvitationList()
    }
  }, [props.open])

  return (
    <Dialog id="CircleAuthSettingModal" className="member-invitation-modal" open={props.open} onClose={handleClose}>
      <section className="member-invitation-modal__header">
        <span className="member-invitation__advertiser">애드써클 초대 수락하기</span>
        <MopIcon name={MOPIcon.CLOSE} onClick={handleClose} size={20} bgColor="#f3f3f6" />
      </section>
      <DialogContent>
        <section className="py-7">
          <div className="invitation-table h-[320px]">
            <div className="invitation-table__header">
              <span>애드써클</span>
              <span>{t('circle.authSettingModal.label.column.authority')}</span>
              <span>초대자(MOP ID)</span>
              <span>E-mail(MOP ID)</span>
              <span>초대 날짜</span>
              <span>수락하기</span>
            </div>
            <div className="invitation-table__body">
              {invitationList &&
                invitationList.length > 0 &&
                invitationList.map((item) => (
                  <div className="invitation-table__row">
                    <div className="invitation-table__row-cell justify-center">
                      <EllipsisText className="text-center" title={item.advertiserName}>
                        {item.advertiserName}
                      </EllipsisText>
                    </div>
                    <div className="invitation-table__row-cell">
                      <TextIcon code={item.authorityType} size={20} />
                      {t(`setting.authority.name.${item.authorityType}`)}
                    </div>
                    <div className="invitation-table__row-cell justify-center">
                      <EllipsisText className="text-center" title={item.inviterEmail}>
                        {item.inviterEmail}
                      </EllipsisText>
                    </div>
                    <div className="invitation-table__row-cell justify-center">
                      <EllipsisText className="text-center" title={item.inviteeEmail}>
                        {item.inviteeEmail}
                      </EllipsisText>
                    </div>
                    <div className="invitation-table__row-cell justify-center">
                      <EllipsisText className="text-center" title={item.inviteDateTime}>
                        {item.inviteDateTime}
                      </EllipsisText>
                    </div>
                    <div className="invitation-table__row-cell justify-center">
                      {item.invitationId && (
                        <>
                          <DecisionButton
                            decision="accept"
                            onClick={() => handleReply(YNFlag.Y, item.invitationId as number)}
                          >
                            수락
                          </DecisionButton>
                          <DecisionButton
                            decision="reject"
                            onClick={() => handleReply(YNFlag.N, item.invitationId as number)}
                          >
                            거절
                          </DecisionButton>
                        </>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </section>
      </DialogContent>
    </Dialog>
  )
}

export default MemberInvitationModal
