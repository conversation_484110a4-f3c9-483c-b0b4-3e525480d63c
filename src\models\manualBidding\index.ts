import { MediaType, YNFlag } from '@models/common'
import { PaginationResponse } from '@models/common/CommonResponse'

export enum BID_CRITERIA {
  AVG_RANK = 'AVG_RANK',
  ROAS = 'ROAS',
  CPA = 'CPA'
}

interface ManualBidBase {
  adId: string
  bidYn: YNFlag
  bidCriteria: BID_CRITERIA
  bidTarget: number
  adgroupName: string
  campaignName: string
  searchKeyword: string
  mediaType: MediaType
  productTitle: string
  productName: string
}

export interface ManualBidListItem extends ManualBidBase {
  adMonitoringId: number
  adgroupType: string
  bidAmount: number
  maxCpcYn: YNFlag
  createdDatetime?: string
  productName: string
}

export interface GetManualBidListQeury {
  advertiserId: number
  orderBy?: string
  pageIndex: number
  pageSize: number
  sorting?: string
  bidYn?: YNFlag
  adTitleId?: string
  mediaType?: string
}

export interface ManualBidSetting {
  bidCriteria: BID_CRITERIA
  bidTarget: number
  incrementalUseYn: YNFlag
  incrementalValue: number
  maxCpc: number
}

export interface CreateManualBidParams extends ManualBidSetting {
  adId: string
  advertiserId: number
  mediaType: MediaType
  searchKeyword: string
  totalKeywordYn: YNFlag
}

export interface ManualBidSettingItem extends CreateManualBidParams {
  accountId: string | null
  adMonitoringId: number | null
  adType: string | null
  useYn: YNFlag
  bidYn: YNFlag
  bidAmount: number
  productTitle: string
  productName: string
  campaignName: string
  adgroupName: string
}

export interface UpdateBidItemYnParams {
  incrementalUseYn?: YNFlag
  bidYn?: YNFlag
}

export type GetTargetBidResponse = PaginationResponse<ManualBidListItem[], 'ads'>

export interface GetKeywordStatsQuery {
  adId: string
  advertiserId: number
  loginMemberId: number
}

export interface ManualBidKeywordData {
  latestStatDate: string
  advertiserId: number
  accountId: string
  campaignId: string
  adgroupId: string
  adId: string
  searchKeyword: string
  totalKeywordYn: YNFlag
  impressions: number
  clicks: number
  cost: number
  conversions: number
  sales: number
  avgRank: number
  cpc: number
  cpa: number
  roas: number
}
