.common-rounded-select.MuiInputBase-root {
  width: 100%;
  height: 32px;
  font-weight: 700;
  color: var(--point_color);
  border: 1px solid #bbbdcd;
  border-radius: 9999px;
  background-color: #fff;
  padding: 0 16px;

  &.MuiInput-underline {
    &::before,
    &::after {
      display: none;
    }
  }

  .MuiSelect-select {
    &:focus {
      background-color: transparent;
    }
    .rounded-select-item {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--font-size, 12px);
      gap: 8px;
    }
  }
  #select-dropdown-icon {
    border: none;
    right: 8px;
  }
}

#rounded-select-options {
  .MuiPopover-paper .MuiMenu-list {
    padding: 0;
  }
  .MuiPaper-rounded {
    border-radius: 0px;
    box-shadow: none;
    border: 1px solid #b5b7c9;
  }

  .MuiListItem-button:hover {
    font-weight: 500;
  }

  .MuiMenuItem-root {
    width: 100%;
    margin: 8px 0;
    position: relative;
    font-size: var(--font-size, 12px);
    font-weight: 400;
    color: var(--point_color);
    text-align: center;
    border-radius: 0px;

    &.Mui-selected {
      font-weight: 700;
      background-color: transparent;
    }
  }
  .rounded-select-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    span {
      min-width: 50px;
      text-align: center;
    }
  }
}
