import React from "react"
import BudgetAnalysis<PERSON>hart from './BudgetAnalysisChart'
import './BudgetListDetails.scss'

import { useTranslation } from 'react-i18next';

interface Props {
  optimizationId: number
  chartData:{
    data: string,
    date_idx: number,
    prediction_date: string
  }[]
  toggleHidden: (_id:number, _name:string) => void
  hiddenToggle: any
}
const BudgetListDetails = ({ chartData, optimizationId, hiddenToggle, toggleHidden }: Props) => {
  const { t } = useTranslation();
  const toggleData = hiddenToggle[optimizationId] || {};
  const toggleChartLine = (event: React.MouseEvent<HTMLButtonElement>) => {
    toggleHidden(optimizationId, event.currentTarget.name)
  }
  const reversed = chartData.slice().reverse()
  return (
    <section className="budget-list-details">
      <div className="toggle-wrapper">
        <div className="toggle-title">
          <span>{t(`rankMaintenance.label.RankMonitoringModal.chart.label.view`)}</span>
        </div>
        <button
          className={`toggle impressions ${toggleData.impressions ? 'on' : 'off'}`}
          name="impressions"
          onClick={toggleChartLine}
        >Impressions</button>
        <button
          className={`toggle clicks ${toggleData.clicks ? 'on' : 'off'}`}
          name="clicks"
          onClick={toggleChartLine}
        >Clicks</button>
        <button
          className={`toggle revenue ${toggleData.revenues ? 'on' : 'off'}`}
          name="revenues"
          onClick={toggleChartLine}
        >Revenue</button>
        <button
          className={`toggle conversions ${toggleData.conversions ? 'on' : 'off'}`}
          name="conversions"
          onClick={toggleChartLine}
        >Conversions</button>
        <button
          className={`toggle top-impression-share ${toggleData.top_imps ? 'on' : 'off'}`}
          name="top_imps"
          onClick={toggleChartLine}
        >Top Impression Share</button>
      </div>
      <div className="chart-panel-wrapper">
        {reversed.map(((datum, idx) => (
          <BudgetAnalysisChart key={idx} toggleData={toggleData} datum={datum} />
        )))}
      </div>
    </section>
  )
}
export default BudgetListDetails
