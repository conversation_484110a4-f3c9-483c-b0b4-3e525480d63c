import { UnitCampaign, CreaeteUnitForm } from '@models/circle'
import { TableTitle, FixedLayoutColumn } from '@components/common/table'
import { MediaIcon, Badge } from '@components/common'
import { useTranslation } from 'react-i18next'
import { MopMedia } from '@models/common'
import { mediaActiveStatus } from '@utils/common/MediaType'
import './CircleListTableFormatter.scss'
import './commonTable.scss'
type TableRow = FixedLayoutColumn<UnitCampaign>

export default class TableFormatter {
  mediaType: MopMedia
  mediaAccountId: string
  accountName: string
  analyticsType: string
  analyticsAccountId: string | null
  analyticsSubId: string | null
  analyticsMetricName: string

  constructor(account: CreaeteUnitForm) {
    this.mediaType = account.mediaType
    this.mediaAccountId = account.mediaAccountId
    this.accountName = account.accountName
    this.analyticsType = account.analyticsType
    this.analyticsAccountId = account.analyticsAccountId
    this.analyticsSubId = account.analyticsSubId
    this.analyticsMetricName = account.analyticsMetricName
  }

  getColumnFormat = (): Array<TableRow> => {
    const { t } = useTranslation()
    const columnAccount = (): TableRow => {
      return {
        title: <TableTitle titleStr="매체 / 계정" />,
        field: 'adType',
        align: 'center',
        sorting: true,
        cellStyle: { width: '15%' },
        render: () => {
          return (
            <div className="media-account-cell">
              <MediaIcon mediaType={this.mediaType} />
              <span className="media-account-cell__name">{this.accountName}</span>
              <span>{this.mediaAccountId}</span>
            </div>
          )
        }
      }
    }
    const columnAdType = (): TableRow => {
      return {
        title: <TableTitle titleStr="광고유형" />,
        field: 'adType',
        align: 'center',
        sorting: true,
        cellStyle: { width: '5%' },
        render: ({ platformType }) => {
          return (
            <div className="cell-body-box">
              {platformType && (
                <Badge
                  size="sm"
                  outlined
                  i18nKey={`common.code.platform.${platformType}`}
                  className="text-active-blue border-active-blue"
                />
              )}
            </div>
          )
        }
      }
    }

    const columnCampaign = (): TableRow => {
      return {
        title: <TableTitle titleStr="캠페인" />,
        field: 'campaignName',
        align: 'center',
        sorting: true,
        cellStyle: { width: '25%' },
        render: (campaign) => {
          return (
            <div className="cell-body-box circle-cell__campaign">
              <span className="circle-cell__campaign-name">{campaign.campaignName}</span>
              <span className="circle-cell__campaign-id">{campaign.campaignId}</span>
            </div>
          )
        }
      }
    }

    const columnActive = (): TableRow => {
      return {
        title: <TableTitle titleStr="ON/OFF" />,
        field: 'campaignId',
        align: 'center',
        sorting: true,
        cellStyle: { width: '5%' },
        render: (campaign) => {
          return <div className="cell-body-box">{mediaActiveStatus(campaign.active, this.mediaType)}</div>
        }
      }
    }

    const columnConversionTool = (): TableRow => {
      return {
        title: <TableTitle titleStr="전환툴" />,
        field: 'active',
        align: 'center',
        sorting: true,
        cellStyle: { width: '5%' },
        render: () => {
          return (
            <div className="cell-body-box">
              <MediaIcon mediaType={this.analyticsType === 'MEDIA' ? this.mediaType : this.analyticsType} />
            </div>
          )
        }
      }
    }

    const columnConversionMetric = (): TableRow => {
      return {
        title: <TableTitle titleStr="전환값" />,
        field: 'active',
        align: 'center',
        sorting: true,
        cellStyle: { width: '15%' },
        render: () => {
          return <div className="cell-body-box">{this.analyticsMetricName}</div>
        }
      }
    }

    return [
      columnAccount(),
      columnAdType(),
      columnCampaign(),
      columnActive(),
      columnConversionTool(),
      columnConversionMetric()
    ]
  }
}
