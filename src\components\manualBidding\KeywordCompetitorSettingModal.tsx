import React, { ReactElement, useCallback, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  IconButton,
  Button,
  TableHead,
  TableBody,
  TableCell,
  TableRow,
  Table,
  Grid,
  TableContainer,
  OutlinedInput
} from '@material-ui/core'
import Checkbox from '@material-ui/core/Checkbox'
import { t } from 'i18next'
import { useRecoilValue } from 'recoil'
import { format } from 'date-fns'
import { SScompetitorsState } from '@store/KeywordRankMonitoring'
import { ShoppingCompetitorInfo } from '@models/rankMaintenance/KeywordRankMonitoring'
import { useToast } from '@hooks/common'
import { updateKeywordRankMonitoringCompetitors } from '@api/manualBidding/RankMonitoring'
import { StatusCode } from '@models/common/CommonResponse'
import CloseIcon from '@components/assets/images/icon_close.png'
import CustomTooltip from '@components/common/CustomTooltip'
import { objectCompare } from '@utils/CompareUtil'

import { DateFnsFormat } from '@models/common/CommonConstants'
import { numberWithCommas } from '@utils/FormatUtil'
import ReportModalTitle from '@components/common/optimization/ReportModalTitle'
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_SA.svg'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
import './KeywordCompetitorSettingModal.scss'

export interface Props {
  onClose: (saveYn: string) => void
  monitoringId: number
  open: boolean
}

const formatDate = (date: string) => {
  return format(new Date(date), DateFnsFormat.YEAR_MONTH)
}
const KeywordCompetitorSettingModal: React.FC<Props> = (props: Props): ReactElement => {
  const savedCompetitors = useRecoilValue(SScompetitorsState)
  const [competitors, setCompetitors] = useState<ShoppingCompetitorInfo[]>(savedCompetitors)
  const { openToast } = useToast()

  const countMonitoringY = useCallback((competitors: ShoppingCompetitorInfo[]) => {
    let monitoringY = 0
    for (const c of competitors) {
      if (c.monitoringYn === 'Y') monitoringY++
    }
    return monitoringY
  }, [])

  const handleSave = async () => {
    if (objectCompare(competitors, savedCompetitors, true)) {
      openToast(t('common.message.notChanged'))
      props.onClose('N')
      return
    }

    if (
      competitors.filter(
        (item) => item.monitoringYn === 'Y' && !(typeof item.alias === 'string' && item.alias.length > 0)
      ).length > 0
    ) {
      openToast(t('rankMaintenance.message.RankMonitoringModal.validation.mandatoryAliasName'))
      return
    }

    try {
      const response = await updateKeywordRankMonitoringCompetitors({
        monitoringId: props.monitoringId,
        competitors: competitors
      })

      if (response.statusCode === StatusCode.SUCCESS) {
        openToast(t('common.message.saveSuccess'))
        props.onClose('Y')
      } else {
        openToast(t('common.message.systemError'))
      }
    } catch (error) {
      openToast(t('common.message.systemError'))
    }
  }

  const handleClickCheckbox = (index: number, event: any) => {
    if (event.target.checked && countMonitoringY(competitors) >= 5) {
      openToast(t('rankMaintenance.message.RankMonitoringModal.validation.overMonitoringMax'))
    } else {
      setCompetitors(
        competitors.map((c: ShoppingCompetitorInfo, cIndex) =>
          cIndex === index ? { ...c, monitoringYn: event.target.checked ? 'Y' : 'N' } : c
        )
      )
    }
  }

  const handleChangeAlias = (index: number, alias: string) => {
    setCompetitors(
      competitors.map((c: ShoppingCompetitorInfo, cIndex) => (cIndex === index ? { ...c, alias: alias } : c))
    )
  }

  return (
    <div>
      <Dialog id="KeywordCompetitorSettingModal" open={props.open} fullScreen onClose={() => props.onClose('N')}>
        <DialogTitle>
          <IconButton
            data-testid="closeIcon"
            className="modal-close"
            aria-label="close"
            onClick={() => props.onClose('N')}
          >
            <img alt="close-image" src={CloseIcon} />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Grid container direction="column">
            <Grid item>
              <ReportModalTitle title="Shopping Ad" icon={<TitleLabelIcon />}>
                <span>{t('rankMaintenance.label.RankMonitoringModal.keywordMonitoringSettingEN')}</span>
                <span className="sub-title">
                  {t('rankMaintenance.label.RankMonitoringModal.keywordMonitoringSettingKO')}
                </span>
              </ReportModalTitle>
            </Grid>
            <Grid item className="competitor-table">
              <TableContainer>
                <Table stickyHeader>
                  <colgroup>
                    <col className="competitor-table-column-ss-checkbox" />
                    <col className="competitor-table-column-product" />
                    <col className="competitor-table-column-alias" />
                  </colgroup>
                  <TableHead>
                    <TableRow>
                      <TableCell align="center" />
                      <TableCell data-testid={`adTitleHeader`} align="center">
                        <ModalTooltip id="product" field="setting" />
                        {t('rankMaintenance.label.RankMonitoringModal.product')}
                      </TableCell>
                      <TableCell data-testid={`aliasHeader`} align="center">
                        <ModalTooltip id="name" field="setting" />
                        {t('rankMaintenance.label.RankMonitoringModal.alias')}
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {competitors.map((competitor, index) => (
                      <TableRow key={`competitors-${index}`}>
                        <TableCell>
                          <Checkbox
                            checked={competitor.monitoringYn === 'Y'}
                            color="default"
                            inputProps={{ 'aria-label': 'checkbox with default color' }}
                            onClick={(event) => {
                              handleClickCheckbox(index, event)
                            }}
                            disableRipple
                          />
                        </TableCell>
                        <TableCell className="competitor-table-body-cell-product">
                          <div className="competitor-product">
                            <img
                              className="competitor-product-image"
                              src={competitor.imageUrl}
                              alt={`competitor-product-image-${index}`}
                            />
                            <div className="competitor-product-detail">
                              <p className="product-title">{competitor.adTitle}</p>
                              <div className="product-info">
                                <div className="product-info-box">
                                  <span className="product-info-label">Price</span>
                                  <span className="product-info-value price">{numberWithCommas(competitor.price)}</span>
                                </div>
                                <div className="product-info-box">
                                  <span className="product-info-label">Registered</span>
                                  <span className="product-info-value">{formatDate(competitor.openDate)}</span>
                                </div>
                                <div className="product-info-box">
                                  <span className="product-info-label">Seller</span>
                                  <span className="product-info-value">{competitor.mallName}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="competitor-table-body-cell-alias">
                          <CustomTooltip title={competitor.alias || ''} placement="bottom-start">
                            <OutlinedInput
                              data-testid={`alias-${index}`}
                              value={competitor.alias || ''}
                              onChange={(event) => {
                                handleChangeAlias(index, event.target.value)
                              }}
                              inputProps={{
                                maxLength: 20
                              }}
                              aria-describedby="outlined-weight-helper-text"
                              labelWidth={0}
                            />
                          </CustomTooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Grid>
          </Grid>
          <div className="saveButtonWrapper">
            <Button
              id="saveButton"
              className="DialogSaveButton"
              data-testid={'saveButton'}
              onClick={handleSave}
              disabled={competitors.length === 0}
            >
              {t(`common.label.button.save`)}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default KeywordCompetitorSettingModal
