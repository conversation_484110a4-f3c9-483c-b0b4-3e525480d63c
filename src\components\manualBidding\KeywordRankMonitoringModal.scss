#SSKeywordRankMonitoringModal {
  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.4);
  }

  .MuiDialog-scrollBody {
    overflow-x: auto;
  }

  .MuiDialogTitle-root {
    height: 0px;
    padding: 0px;

    .modal-close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      border-radius: 50%;
      background: #f3f3f6;
    }
  }

  .MuiDialog-paper {
    width: 1600px;
    max-width: 1600px;
    height: fit-content;
    background-color: var(--bg-gray-light);
    box-sizing: border-box;

    .MuiDialogContent-root {
      padding: 16px 47px 25px;

      .search-label-container,
      .search-input-container {
        width: 100%;
        .search-advertiser,
        .search-monitoring-date {
          width: 100%;
          display: flex;
          justify-content: center;
        }

        .MuiBox-root {
          display: flex;
          align-items: center;

          height: 100%;
          .MuiFormLabel-root {
            display: flex;
            align-items: center;

            font-size: 13px;
            font-weight: 300;
            color: var(--color-white);

            & > span {
              padding-left: 10px;
              font-size: 13px;
              font-weight: 400;
              color: var(--point_color);
            }
          }
        }

        .MuiGrid-item:last-child {
          flex: 1 1 auto;
        }
      }

      .search-label-container {
        height: 22px;
        margin-top: 12px;
        background-color: #73798c;
        .shopping-device,
        // .search-keyword,
        .shopping-adId {
          .MuiFormLabel-root {
            padding-right: 62px;
          }
        }
        .shopping-keyword {
          .MuiFormLabel-root {
            padding-right: 0px;
          }
        }
      }

      .shopping-label {
        height: 37px;
        display: flex;
        align-self: center;
        justify-content: center;

        // svg {
        //   width: 20px;
        //   height: 20px;
        //   display: none;
        //   margin-right: 5px;
        // }

        label {
          display: flex;
          align-items: center;
          height: 22px;
          font-size: 13px;
          font-weight: 300;
          color: #fff;
        }
      }

      .shopping-advertisement {
        width: 166px;
      }
      .shopping-media {
        width: 130px;
      }
      .shopping-device {
        width: 150px;
      }
      .shopping-adId {
        width: 320px;
      }
      .shopping-keyword {
        width: 240px;
      }
      .shopping-period {
        width: 380px;
        box-sizing: border-box;
      }
      .shopping-view {
        width: 120px;
      }
      .search-input-container {
        height: auto;
        .MuiGrid-item {
          background-color: #fff;
        }
        .shopping-input {
          display: flex;
          border-right: 1px solid #b5b7c9;
          border-bottom: 1px solid #b5b7c9;

          .MuiSelect-select {
            width: 100%;
            padding: 0;
            font-weight: 300;
          }

          .menuItem {
            .MuiPaper-rounded {
              border-radius: 0px;
              box-shadow: none;
              border: 1px solid #b5b7c9;
            }

            .MuiMenuItem-root {
              width: 100%;
              position: relative;
              font-size: 12px;
              font-weight: 200;
              color: var(--point_color);
              text-align: center;
              border-radius: 0px;

              &.Mui-selected {
                font-weight: 500;
                background-color: transparent;
              }
            }
          }

          justify-content: center;
          align-items: center;
          font-size: 14px;
          color: var(--point_color);

          .MuiInputBase-root {
            width: 100%;
            height: 100%;

            &.MuiInput-underline {
              &::before,
              &::after {
                display: none;
              }
            }

            .MuiSelect-icon {
              top: calc(50% - 12px);
              right: 15px;
              display: inline-block;
              width: 16px;
              height: 16px;
              border-left: 1px solid var(--point_color);
              border-top: 1px solid var(--point_color);
              transform: rotate(-135deg);
              opacity: 0.6;

              &.MuiSelect-iconOpen {
                transform: rotate(45deg);
                top: calc(50% - 3px);
              }

              path {
                display: none;
              }
            }

            .search-input-dropdown {
              width: 62px;
              min-width: 62px;
              height: 34px;
              position: absolute;
              right: 0px;
              border-left: 1px solid #b5b7c9;
              pointer-events: none;

              svg {
                position: absolute;
                top: calc(50% - 12px);
                left: 24px;
                display: inline-block;
                min-width: 16px;
                min-height: 16px;
                width: 16px;
                height: 16px;
                border-left: 1px solid var(--point_color);
                border-top: 1px solid var(--point_color);
                transform: rotate(-135deg);
                opacity: 0.6;

                path {
                  display: none;
                }
              }

              &.search-input-dropdown-open {
                background-color: var(--point_color);

                svg {
                  transform: rotate(45deg);
                  top: calc(50% - 3px);

                  border-left: 1px solid var(--color-white);
                  border-top: 1px solid var(--color-white);
                }
              }
            }
          }

          .MuiInputBase-input {
            font-size: 14px;
            font-weight: 300;
            color: var(--point_color);
            box-sizing: border-box;
            text-align: center;

            &:focus {
              background: none;
            }
          }
        }

        .MuiGrid-item:first-child {
          .shopping-input {
            border-left: 1px solid #b5b7c9;
          }
        }

        .shopping-device,
        .shopping-keyword,
        .shopping-adId {
          .MuiSelect-select {
            padding-right: 62px;
          }
        }

        #keyword-rank-monitoring-search-period {
          width: 498px !important;
          height: 34px !important;
          border-right: none;
          background-color: var(--color-white);

          input.MuiInputBase-input {
            font-size: 14px;
            font-weight: 300;
            color: var(--point_color);
            &#keyword-rank-monitoring-search-period-start-date {
              padding: 0 0 0 62px;
            }
            &#keyword-rank-monitoring-search-period-end-date {
              padding: 0 62px 0 0;
            }
          }

          .date-period-picker-searching-dropdown {
            width: 62px;
            min-width: 62px;
            height: 34px;
            svg {
              min-width: 16px;
              min-height: 16px;
              width: 16px;
              height: 16px;
              top: calc(50% - 12px);
              left: 24px;
              &.date-period-picker-searching-dropdown-expand {
                top: calc(50% - 3px);
              }
            }
          }
        }

        .shopping-view {
          .MuiToggleButtonGroup-root {
            width: 100%;
            height: 100%;
            .MuiButtonBase-root {
              width: 50%;
              height: 100%;
              padding: 0;
              margin: 0;
              border: none;
              border-radius: 0;
              .rank-chart-icon {
                width: 22px;
                height: 22px;
                fill: var(--point_color);
              }
              .rank-table-icon {
                width: 22px;
                height: 22px;
                stroke: var(--point_color);
              }
              &.Mui-selected {
                background-color: var(--point_color);
                .rank-chart-icon {
                  fill: var(--color-white);
                }
                .rank-table-icon {
                  stroke: var(--color-white);
                }
              }
            }
          }
        }
      }

      .download-button-row {
        margin-top: 5px;
        height: 44px;
        align-items: center;
        .download-button {
          display: none;
          width: 24px;
          height: 24px;
          min-width: 24px;
          padding: 0px;
          span {
            width: 24px;
            height: 24px;
          }
          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }
}

.keyword-rank-monitoring-filter-popover {
  .MuiPopover-paper {
    margin-left: -1px;
    box-sizing: content-box;
  }
  .MuiPaper-rounded {
    border-radius: 0px !important;
    box-shadow: none !important;
    border: 1px solid #b5b7c9 !important;

    :hover {
      border-radius: 0px !important;
    }
  }

  .MuiMenuItem-root {
    width: 100%;
    height: 34px;
    font-size: 12px !important;
    font-weight: 200 !important;
    color: var(--point_color);
    text-align: center;
    border-radius: 0px;

    &.Mui-selected {
      font-weight: 500 !important;
      background-color: transparent !important;
    }
  }

  .MuiListItem-button:hover {
    font-weight: 500;
  }

  .MuiMenu-list {
    &::-webkit-scrollbar-thumb {
      background-color: var(--point_color);
    }
  }
}

.keyword-rank-monitoring-shoppingperiod-date-popover {
  .MuiPickersBasePicker-pickerView {
    min-width: 406px;
    max-width: 406px;
    width: 406px;
  }
}
