import tw from 'twin.macro'
import styled from '@emotion/styled'
import { TransText } from '@components/common'
import { ReactComponent as FlowPC } from '@assets/images/intro/img-our-technology-pc.svg'
import { ReactComponent as FlowMobile } from '@assets/images/intro/img-our-technology-mo.svg'

const Values = () => {
  return (
    <div className='text-white'>
      <h2 className='mb-14 text-xl font-medium text-center text-landing-red'>Values</h2>
      <ol className='mx-5 md:mx-10 py-14 px-7 md:py-40 md:px-28 flex flex-col gap-20 md:gap-24 bg-black rounded-lg'>
        <li className='flex flex-col gap-5 lg:flex-row lg:gap-28'>
          <span className='text-base md:text-4xl md:font-medium'>01</span>
          <div className='flex flex-col gap-6'>
            <h3>
              <TransText as='h3' className='text-lg md:text-4xl font-medium' i18nKey='landing.values.0.title' />
            </h3>
            <p>
              <TransText as='p' className='mt-[-15px] text-sm md:mt-0 md:text-lg font-normal leading-relaxed' i18nKey='landing.values.0.desc' />
            </p>
          </div>
        </li>
        <li className='flex flex-col gap-5 lg:flex-row lg:gap-28'>
          <span className='text-base md:text-4xl md:font-medium'>02</span>
          <div className='flex flex-col gap-6'>
            <h3>
              <TransText as='h3' className='text-lg md:text-4xl font-medium' i18nKey='landing.values.1.title' />
            </h3>
            <p>
              <TransText as='p' className='mt-[-15px] text-sm md:mt-0 md:text-lg font-normal leading-relaxed' i18nKey='landing.values.1.desc' />
            </p>
          </div>
        </li>
        <li className='flex flex-col gap-5 lg:flex-row lg:gap-28'>
          <span className='text-base md:text-4xl md:font-medium'>03</span>
          <div className='flex flex-col gap-6'>
            <h3>
              <TransText as='h3' className='text-lg md:text-4xl font-medium' i18nKey='landing.values.2.title' />
            </h3>
            <p>
              <TransText as='p' className='mt-[-15px] text-sm md:mt-0 md:text-lg font-normal leading-relaxed' i18nKey='landing.values.2.desc' />
            </p>
          </div>
        </li>
      </ol>
    </div>
  )
}

export default Values
