import React, { ReactElement, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ListItemFilterGroup, ListItemFilter } from '@components/common/filter'
import { MenuItem } from '@material-ui/core';

import { useRecoilValue, useRecoilState } from 'recoil';
import { urlDetectionFilterQuery, queryStringOptName, queryStringName, filteredUrlAnomalyNameItems, filteredUrlAnomalyOptNameItems } from '@store/AnomalyDetection'
import SelectBottom from '@components/common/SelectBottom'
import { MopSearch } from '@components/common';

import { QueryKeys } from '@models/anomalyDetection'
import { getMediaItems, getAnomalyTypeItems } from '@utils/CodeUtil';
import { SelectDropdownIcon } from '@components/common/icon'
import './UrlDetectionTableFilter.scss'

const UrlDetectionTableFilter: React.FC = (): ReactElement => {
  const { t } = useTranslation();
  const [query, setQuery] = useRecoilState(urlDetectionFilterQuery)
  const filteredOptItems = useRecoilValue(filteredUrlAnomalyOptNameItems)
  const filteredNameItems = useRecoilValue(filteredUrlAnomalyNameItems)

  const [searchOptName, setSearchOptName] = useRecoilState(queryStringOptName)
  const [searchName, setSearchName] = useRecoilState(queryStringName)

  const [filter, setFilter] = useState<QueryKeys>({
    optimizationName: 'ALL',
    mediaType: 'ALL',
    name: 'ALL',
    anomalyType: 'ALL'
  })

  const resetQuery = (query: Partial<QueryKeys>, filter: QueryKeys, keys: (keyof QueryKeys)[]) => {
    keys.forEach((key) => {
      if (query[key]) {
        delete query[key]
        filter[key] = 'ALL'
      }
    })
    return { query, filter }
  }

  const selectFilter = (e: React.ChangeEvent<{
    name?: string | undefined;
    value: unknown;
  }>) => {
    const name = e.target.name as keyof QueryKeys;
    let newQuery = { ...query }
    let newFilter = { ...filter }
    if (e.target.value === 'ALL' && newQuery[name]) {
      delete newQuery[name]
      setQuery(newQuery)
    } else {
      // let removed
      if (name === 'optimizationName') {
        const removed = resetQuery(newQuery, newFilter, ['mediaType', 'anomalyType', 'name'])
        newQuery = removed.query
        newFilter = removed.filter
      }
      if (name === 'mediaType') {
        const removed = resetQuery(newQuery, newFilter, ['anomalyType', 'name'])
        newQuery = removed.query
        newFilter = removed.filter
      }
      if (name === 'anomalyType') {
        const removed = resetQuery(newQuery, newFilter, ['name'])
        newQuery = removed.query
        newFilter = removed.filter
      }
      setQuery({ ...newQuery, [name]: e.target.value })
    }
    setFilter({ ...newFilter, [name]: e.target.value })
  }

  return (
    <ListItemFilterGroup customClass="url-detection-filter-group">
      <ListItemFilter
        label={t('anomalyDetection.label.filter.optimization')}
        name="optimization"
        value={filter.optimizationName}
        onChange={selectFilter}
        isCustomSelect
      >
        <SelectBottom
          displayEmpty
          onChange={selectFilter}
          name="optimizationName"
          value={filter.optimizationName}
          MenuProps={{
            className: 'filter-options-popover',
            anchorOrigin: { vertical: 24, horizontal: 'left' }
          }}
          IconComponent={(props) => <SelectDropdownIcon {...props} />}
        >
          <MopSearch
            placeholder={'Search'}
            value={searchOptName}
            onChange={(e) => setSearchOptName(e.target.value)}
            onSearch={() => setSearchOptName(searchOptName)}
            onFocus={() => setSearchOptName('')}
          />
          <MenuItem value={'ALL'} className="filter-item">
            {t('common.label.filter.all')}
          </MenuItem>
          {filteredOptItems.map((item, i) => (
            <MenuItem
              className="filter-item optName"
              key={`${item.optimizationId}-${i}`}
              value={item.optimizationName}
            >
              <div className="filter-item-group">
                <span className="filter-item-group__head">{item.optimizationId}</span>
                <span>{item.optimizationName}</span>
              </div>
            </MenuItem>
          ))}
        </SelectBottom>
      </ListItemFilter>
      <ListItemFilter
        label={t('anomalyDetection.label.filter.media')}
        name="mediaType"
        value={filter.mediaType}
        onChange={selectFilter}
      >
        {getMediaItems().map((media) => (
          <MenuItem key={media.type} value={media.type}>
            {media.name}
          </MenuItem>
        ))}
      </ListItemFilter>
      <ListItemFilter
        label={t('anomalyDetection.label.filter.type')}
        name="anomalyType"
        value={filter.anomalyType}
        onChange={selectFilter}
      >
        {getAnomalyTypeItems().map((item) => (
          <MenuItem key={item.type} value={item.name}>
            {t(`anomalyDetection.label.anomalyType.${item.name}`)}
          </MenuItem>
        ))}
      </ListItemFilter>
      <ListItemFilter
        label={t('anomalyDetection.label.filter.name')}
        name="name"
        value={filter.name}
        onChange={selectFilter}
        isCustomSelect
      >
        <SelectBottom
          displayEmpty
          onChange={selectFilter}
          value={filter.name}
          name="name"
          MenuProps={{
            className: 'filter-options-popover',
            anchorOrigin: { vertical: 24, horizontal: 'left' }
          }}
          IconComponent={(props) => <SelectDropdownIcon {...props} />}
        >
          <MopSearch
            id="anomaly-search-name"
            placeholder={'Search'}
            value={searchName}
            onChange={(e) => setSearchName(e.target.value)}
            onSearch={() => setSearchName(searchName)}
            onFocus={() => setSearchName('')}
          />
          <MenuItem value={'ALL'} className="filter-item">
            {t('common.label.filter.all')}
          </MenuItem>
          {filteredNameItems?.map(item => (
            <MenuItem key={item.id} value={item.name}>
              <div className="filter-item-group">
                <span className="filter-item-group__head">{item.name}</span>
                <span>{item.id}</span>
              </div>
            </MenuItem>
          ))}
        </SelectBottom>
      </ListItemFilter>
    </ListItemFilterGroup>
  )
}

export default UrlDetectionTableFilter;