/* istanbul ignore file */
import * as yup from 'yup'
import { t } from 'i18next'
import { ConstraintType, CONSTRAINT } from '@models/common/ContraintsByCurrency'

export const constraintSchema = (min: number, max: number, unit: number, type: ConstraintType) => {
  return yup
    .number()
    .required('rankMaintenance.toast.constraint.empty')
    .min(min, 'rankMaintenance.toast.constraint.empty')
    .max(max, 'rankMaintenance.toast.constraint.empty')
    .test('unit', 'rankMaintenance.toast.constraint.unit', (value) => (value ? (value / unit) % 1 === 0 : false))
}

export const createRankSchema = yup.object().shape({
  keywordId: yup.string().required(
    t('common.message.validation.required', {
      param: t('common.label.keywordId')
    })
  ),
  keywordName: yup.string().required(
    t('common.message.validation.required', {
      param: t('common.label.keyword')
    })
  ),
  deviceType: yup.string().required(
    t('common.message.validation.required', {
      param: t('common.label.device')
    })
  ),
  mediaType: yup.string().required(
    t('common.message.validation.required', {
      param: t('common.label.media')
    })
  )
})
