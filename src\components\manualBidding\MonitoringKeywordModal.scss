#RankMaintenanceMonitoringKeywordDialog {
  .MuiBackdrop-root {
    background-color: rgba(0, 0, 0, 0.4);
  }

  .MuiDialog-scrollBody {
    overflow-x: auto;
  }

  .MuiDialogTitle-root {
    height: 0px;
    padding: 0px;

    .modal-close {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 20px;
      right: 20px;
      border-radius: 50%;
      background: #f3f3f6;
    }
  }

  .MuiDialogContent-root {
    padding: 0;
  }
  .MuiDialog-paper {
    width: 1600px;
    max-width: 1600px;
    height: 870px;
    box-sizing: border-box;
    padding: 30px 47px 0px;

    background-color: var(--bg-gray-light);
    .title {
      font-size: 25px;
      font-weight: 900;
      color: var(--point_color);
    }

    .search-label-container,
    .search-input-container {
      width: 100%;
      > .MuiGrid-item {
        width: 50%;
      }
      .search-advertiser,
      .search-monitoring-date {
        width: 100%;
        display: flex;
        justify-content: center;
      }

      .MuiBox-root {
        display: flex;
        align-items: center;

        height: 100%;
        .MuiFormLabel-root {
          display: flex;
          align-items: center;

          font-size: 13px;
          font-weight: 300;
          color: var(--color-white);

          & > span {
            padding-left: 10px;
            font-size: 13px;
            font-weight: 400;
            color: var(--point_color);
          }
        }
      }
    }

    .search-label-container {
      height: 22px;
      margin-top: 12px;
      background-color: #73798c;
    }
    .search-input-container {
      height: 36px;
      border: 1px solid #b5b7c9;
      border-top: none;
      .MuiGrid-item {
        background-color: #fff;
      }
      .MuiInputBase-root {
        &.MuiInput-underline {
          &::before,
          &::after {
            display: none;
          }
        }
      }
      .MuiInputBase-input {
        font-size: 14px;
        font-weight: 300;
        color: var(--point_color);
        box-sizing: border-box;
        text-align: center;

        &:focus {
          background: none;
        }
      }
      .search-monitoring-date {
        .MuiFormControl-root {
          width: 100%;
          height: 100%;
          .MuiInputBase-root {
            width: 100%;
            height: 100%;

            &.MuiInput-underline {
              &::before,
              &::after {
                display: none;
              }
            }
          }
        }
        #rank-maintenance-monitioring-date {
          width: 100% !important;
          height: 100% !important;
          border-left: 1px solid #b5b7c9;
          .MuiInputBase-input {
            padding: 0;
          }
          .date-picker-searching-dropdown {
            width: 65px;
            min-width: 65px;
            height: 100%;
            border-left: 1px solid #b5b7c9;
            svg {
              min-width: 12px;
              min-height: 12px;
              width: 15px;
              height: 15px;
              top: calc(50% - 10px);
              left: 26px;
              &.date-picker-searching-dropdown-expand {
                top: calc(50% - 2px);
              }
            }
          }

          &.MuiInputBase-adornedEnd {
            .date-picker-searching-dropdown {
              right: 0px;
            }
          }
        }
      }
    }

    .download-button-row {
      margin-top: 5px;
      height: 44px;
      align-items: center;
      .download-button {
        display: none;
        width: 24px;
        height: 24px;
        min-width: 24px;
        padding: 0px;
        span {
          width: 24px;
          height: 24px;
        }
        svg {
          width: 24px;
          height: 24px;
        }
      }
    }
    #DataGridWrapper {
      height: 580px;
      width: 100%;
      .MuiDataGrid-window {
        border-bottom: 2px solid var(--point_color);
      }
      .MuiDataGrid-root {
        border: none;
      }
      .MuiDataGrid-window {
        &::-webkit-scrollbar-thumb {
          background-color: var(--point_color);
        }
        &::-webkit-scrollbar-track {
          background-color: transparent;
        }
      }
      .MuiDataGrid-columnHeaderWrapper {
        > div {
          background-color: var(--point_color);
        }
      }
      .MuiDataGrid-columnsContainer {
        border-bottom: none;
      }
      .MuiDataGrid-columnSeparator {
        display: none !important;
      }
      .MuiDataGrid-columnHeader {
        padding: 0px !important;
        .MuiDataGrid-columnHeaderTitleContainer {
          padding: 0px;
          & > span {
            width: 100%;
            height: 100%;
            font-weight: 400;
            font-size: 13px;
            color: var(--color-white);
            display: inline-block;
            text-align: center;
            &.monitoring-keyword-separate {
              font-weight: 700;
            }
            &.header-tooltip {
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
        .MuiDataGrid-columnHeaderTitle {
          font-weight: 300;
          font-size: 14px;
          color: var(--color-white);
        }
        &:focus-within {
          outline: none;
        }
      }
      .MuiDataGrid-cell {
        padding: 0px;
        border-bottom: 1px solid #8a8f9f;
        & > span {
          width: 100%;
          height: 100%;
          font-weight: 400;
          font-size: 13px;
          color: var(--point_color);
          display: inline-block;
          text-align: center;
          &.monitoring-keyword-label {
            font-size: 13px;
            font-weight: 400;
            background-color: var(--bg-gray-light);
          }
          &.monitoring-keyword-label-name {
            cursor: pointer;
            text-decoration: underline;
            span {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          &.monitoring-keyword-separate {
            font-weight: 700;
          }
        }
        &:has(.monitoring-keyword-separate) {
          border-left: 1px solid var(--point_color);
        }
      }
      .MuiDataGrid-iconButtonContainer {
        display: none;
      }

      .MuiDataGrid-cell:focus {
        outline: none !important;
      }

      .MuiDataGrid-row:nth-child(even) {
        .monitoring-keyword-rank {
          background-color: var(--color-white);
        }
      }
    }
  }
}

.rank-maintenance-monitioring-date-popover {
  .MuiPickersBasePicker-pickerView {
    max-width: 751px !important;
    width: 751px !important;
  }
}
