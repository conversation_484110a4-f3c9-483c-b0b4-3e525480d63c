import * as ApiUtil from '@utils/ApiUtil';
import { getSearchOptimizations } from './SearchOptimization';

describe('SearchOptimization', () => {
  const mockCallApi = jest.spyOn(ApiUtil, 'callApi');

  it('8 - 최적화 목록 조회 응답이 정상인 경우, 최적화 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'Y',
      statusCode: 'SUCCESS',
      data: {
        totalCount: 1,
        optimizations: [
          {
            optimizationId: 10,
            optimizationName: '최적화10',
            mediaType: 'KAKAO',
            bidStartDate: '2022-05-22',
            bidEndDate: '2022-05-23',
            dailyBudget: 1000000,
            optimizationGoal: 'KPIS',
            bidYn: 'Y',
            status: 'BIDDING',
            errorStatus: 'BUDGET_LACK',
          },
        ],
      },
    };

    const requestParam = {
      advertiserId: 1,
      bidStartDate: '2022-05-22',
      bidEndDate: '2022-05-23',
      mediaType: 'NAVER',
      pageSize: 10,
      pageIndex: 1,
    };

    const response = {
      totalCount: responseMock.data.totalCount,
      pageSize: requestParam.pageSize,
      pageIndex: requestParam.pageIndex,
      optimizations: responseMock.data.optimizations,
      currentAdgroupsCount: 0,
      maxAdgroupsCount: 50,
    };

    mockCallApi.mockResolvedValue(responseMock);
    const searchOptimizations = await getSearchOptimizations(requestParam, true);
    expect(searchOptimizations).toEqual(response);
  });

  it('8 - 최적화 목록 조회 응답이 비정상인 경우, totalCount가 0인 목록을 리턴한다.', async () => {
    const responseMock = {
      successOrNot: 'N',
      statusCode: 'FAIL',
      data: {},
    };

    const requestParam = {
      advertiserId: 1,
      bidStartDate: '2022-05-22',
      bidEndDate: '2022-05-23',
      mediaType: 'NAVER',
      pageSize: 10,
      pageIndex: 1,
    };

    const response = {
      totalCount: 0,
      pageSize: requestParam.pageSize,
      pageIndex: 1,
      optimizations: [],
      currentAdgroupsCount: 0,
      maxAdgroupsCount: 50,
    };

    mockCallApi.mockResolvedValue(responseMock);
    const searchOptimizations = await getSearchOptimizations(requestParam, true);
    expect(searchOptimizations).toEqual(response);
  });
});
