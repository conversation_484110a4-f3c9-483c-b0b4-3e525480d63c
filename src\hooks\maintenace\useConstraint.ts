import { ValidationError } from 'yup'
import { t } from 'i18next'
import { ConstraintType, CONSTRAINT } from '@models/common/ContraintsByCurrency'
import { useAuthority, useToast } from '@hooks/common'
import { numberWithCommas } from '@utils/FormatUtil'

import { constraintSchema } from '@utils/validation/rankMaintenance'

const useConstraint = () => {
  const { openI18nToast } = useToast()
  const { advertiser } = useAuthority()
  function getConstraint<T extends boolean | undefined = false>(type: ConstraintType, comma?: T) {
    const { min, max, unit } = CONSTRAINT[advertiser.advertiserCurrencyCode][type]
    return {
      min: comma ? numberWithCommas(min) : min,
      max: comma ? numberWithCommas(max) : max,
      unit: comma ? numberWithCommas(unit) : unit
    } as unknown as Record<string, T extends true ? string : number>
  }

  async function checkConstraintValue(value: number, type: ConstraintType) {
    const { min, max, unit } = getConstraint(type)
    try {
      await constraintSchema(min, max, unit, type).validate(value)
      return true
    } catch (err) {
      if (err instanceof ValidationError) {
        const withComma = getConstraint(type, true)
        const toastOpt = err.message.includes('unit')
          ? { type: t(`rankMaintenance.toast.constraint.${type}`), unit: withComma.unit }
          : { type: t(`rankMaintenance.toast.constraint.${type}`), min: withComma.min, max: withComma.max }
        openI18nToast(err.message, toastOpt)
      }
      return false
    }
  }
  return {
    getConstraint,
    checkConstraintValue
  }
}

export default useConstraint
