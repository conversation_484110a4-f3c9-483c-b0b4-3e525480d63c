#KeywordRankMonitoringChart {
  background-color: var(--color-white);
  .chart-label-container {
    height: 22px;
    background-color: var(--point_color);
    box-sizing: border-box;

    .chart-label {
      display: flex;
      align-self: center;
      justify-content: center;
      margin-right: 5px;

      // svg {
      //   width: 20px;
      //   height: 20px;
      //   display: none;
      //   margin-right: 5px;
      // }

      label {
        display: flex;
        align-items: center;
        height: 22px;
        font-size: 13px;
        font-weight: 300;
        color: #fff;
      }
    }
  }
  .chart-input-container {
    .MuiGrid-root {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .chart-monitoring-onoff {
      height: 50px;
      .MuiSwitch-root {
        width: auto;
        height: auto;
        padding: 0;
        margin: 0;
        align-items: center;
        display: flex;

        .MuiSwitch-switchBase {
          left: 2px;
          padding: 0;

          .MuiSwitch-thumb {
            position: relative;
            top: 2px;
            width: 14px;
            height: 14px;
            padding: 0;
            background-color: #ffffff;
            opacity: 1;
            box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
          }

          &.Mui-checked {
            transform: translateX(18px);
          }
        }

        .MuiSwitch-track {
          width: 36px;
          height: 18px;
          padding: 0;
          border-radius: 10px;
          background-color: #707395;
          opacity: 1;
          border: 1px solid #707395;
        }

        .Mui-checked {
          & + .MuiSwitch-track {
            background-color: var(--status-active);
            opacity: 1;
            border: 1px solid var(--status-active);
          }
        }

        .Mui-disabled {
          .MuiSwitch-thumb {
            background-color: #ccc;
            opacity: 1;
          }

          & + .MuiSwitch-track {
            background-color: #fff;
            opacity: 1;
            border: 1px solid #959595;
          }
        }
      }
    }
    .chart-setting {
      svg {
        width: 22px;
        height: 22px;
        cursor: pointer;
      }
    }
    .chart-onoff-button {
      padding: 0px;
      width: 102px;
      height: 30px;
      border-radius: 13.5px;
      border: none;
      color: var(--color-white);
      font-size: 15px;
      font-weight: 300;
      height: 27px;
      line-height: 27px;
      padding: 0 10px;
      text-align: center;
      text-transform: none;
      background-color: #2b2b2b;
      opacity: 0.6;
      &.Mui-selected {
        opacity: 1;
        &.chart-onoff-button-current {
          background-color: var(--color-blue-darker);
        }
        &.chart-onoff-button-target {
          background-color: #4373b9;
        }
        &.chart-onoff-button-bidAmount {
          background-color: #838da4;
        }
      }
      &:not(:first-child) {
        margin-left: 12px;
      }
      svg {
        margin-right: 5px;
      }
    }

    .chart-competitor-onoff-button {
      padding: 0px;
      width: 102px;
      height: 30px;
      border-radius: 13.5px;
      border: none;
      color: var(--color-white);
      font-size: 15px;
      font-weight: 300;
      height: 27px;
      line-height: 27px;
      padding: 0 10px;
      text-align: center;
      text-transform: none;
      background-color: #2b2b2b;
      opacity: 0.6;
      span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      &.Mui-selected {
        opacity: 1;
        &:nth-child(1) {
          background-color: #b41a3e;
        }
        &:nth-child(2) {
          background-color: #eb5e24;
        }
        &:nth-child(3) {
          background-color: #f3aa24;
        }
        &:nth-child(4) {
          background-color: #9e569e;
        }
        &:nth-child(5) {
          background-color: #04746a;
        }
      }
      &.Mui-disabled {
        background-color: #ccc;
        opacity: 1;
        pointer-events: auto;
      }

      &:not(:first-child) {
        margin-left: 8px;
      }
    }
  }
  .chart-label-container,
  .chart-input-container {
    .chart-view-onoff {
      width: 355px;
      padding: 0 17px;
      display: flex;
      justify-content: center;
    }
    .chart-competitor-onoff {
      padding: 0 17px;
      &.chart-competitor-0 {
        // display: none;
      }
      &.chart-competitor-1 {
        // width: 170px;
      }
      &.chart-competitor-2 {
        width: calc(102px * 2 + 8px + 34px);
      }
      &.chart-competitor-3 {
        width: calc(102px * 3 + 8px * 2 + 34px);
      }
      &.chart-competitor-4 {
        width: calc(102px * 4 + 8px * 3 + 34px);
      }
      &.chart-competitor-5 {
        width: calc(102px * 5 + 8px * 4 + 34px);
      }
    }

    .chart-monitoring-onoff {
      width: 80px;
    }

    .chart-setting {
      width: 80px;
    }

    .MuiGrid-container {
      width: auto;
      margin-left: auto;
    }
  }

  .chart-view-onoff {
    .MuiToggleButtonGroup-root {
      justify-content: center;
    }
  }
  #keyword-rank-monitoring-chart {
    padding: 5px 10px;
    .chart-y-axis-title {
      width: 100%;
      display: flex;
      > span {
        color: var(--point_color);
        font-size: 10px;
        font-weight: 700;
        line-height: 1;
        &:last-child {
          margin-left: auto;
        }
      }
    }
  }
}

#keyword-rank-monitoring-chart-tooltip {
  border-radius: 8px;
  box-shadow: 0 5px 15px 0 rgba(68, 68, 79, 0.7);
  box-sizing: border-box;

  thead {
    th {
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
      .tooltop-title {
        min-width: 110px;
        height: 24px;
        padding: 0 8px;
        font-size: 14px;
        color: var(--color-white);
        text-align: center;
        border-radius: 12px;
        background-color: var(--point_color);
        justify-content: center;
        display: inline-flex;
        align-items: center;
      }
    }
  }
  tbody {
    td {
      display: flex;
      font-weight: 500;
      .tooltop-label {
        max-width: 240px;
        margin-right: 50px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .tooltop-value {
        margin-left: auto;
      }
    }
    tr:first-child {
      td {
        margin-top: 10px;
      }
    }
  }
}
