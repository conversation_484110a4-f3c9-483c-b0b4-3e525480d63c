import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next'
import { Circle, UnitCounts, AuthCounts, CircleStatus } from '@models/circle'
import { TableTitle, FixedLayoutColumn } from '@components/common/table'
import { ReactComponent as EditIcon } from '@components/assets/images/icon_edit.svg';
import { TextIcon, MediaIcon } from '@components/common/icon';
import { ArrowRightButton } from '@components/common/buttons'
import CommonTooltip from '@components/common/CommonTooltip';
import InnerHtml from '@components/common/InnerHtml';
import { ReactComponent as AdviceMarkIcon } from '@components/assets/images/advicemark.svg'
import './CircleListTableFormatter.scss';
import { trimText } from '@utils/FormatUtil'
import TooltipCard from '@components/common/tooltip/TooltipCard';
import { useAuthority, useToast } from '@hooks/common';
import TagManager from 'react-gtm-module'
import { ProBadge, LiteBadge } from '@components/common/BaseChip'
import PlanButton from '@components/common/buttons/PlanButton'
import { SubscriptionProductType } from '@models/common/Advertiser'
import { stat } from 'fs';
type TableRow = FixedLayoutColumn<Circle>
export default class TableFormatter {
  constructor() {
    this.field = ''
    this.order = 'asc'
  }
  field: string
  order: 'asc' | 'desc'
  handleDelete(id: number) {}
  handleEdit(_circle: Circle) {}
  handleAuthModal(_advertiserId: number, _advertiserName: string) {}
  getColumnFormat = (): Array<TableRow> => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const { hasSystemViewerAuthority, advertiserList } = useAuthority()
    const { openToast } = useToast()

    const columnStatus = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('circle.label.columnHeader.status')}
            children={
              <CommonTooltip
                id={`status-info-tooltip`}
                title={<TooltipCard tKey="circle.tooltip.list.status" type="defineList" />}
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'status',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'status' ? this.order : undefined,
        cellStyle: {
          width: '5%'
        },
        render: ({ status }) => {
          return <span className={`status ${status}`}>{t(`circle.label.filter.list.status.${status}`)}</span>
        }
      }
    }

    const columnId = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.label.columnHeader.advertiserId')} />,
        field: 'advertiserId',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'advertiserId' ? this.order : undefined,
        cellStyle: {
          width: '5%'
        },
        render: (rowData) => {
          return <div className="cell-body-box">{rowData.advertiserId}</div>
        }
      }
    }

    const columnPlan = (): TableRow => {
      return {
        title: <TableTitle titleStr={t('circle.label.columnHeader.plan')} />,
        field: 'subscriptionProductType',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'subscriptionProductType' ? this.order : undefined,
        cellStyle: {
          width: '4%'
        },
        customSort: (a, b) => {
          const advertiserA = advertiserList?.find(adv => adv.advertiserId === a.advertiserId)
          const advertiserB = advertiserList?.find(adv => adv.advertiserId === b.advertiserId)
          
          const getTypeWeight = (type?: SubscriptionProductType) => {
            if (type === SubscriptionProductType.PRO) return 3
            if (type === SubscriptionProductType.LITE) return 2
            if (type === SubscriptionProductType.BASIC) return 1
            return 0
          }
          
          const weightA = getTypeWeight(advertiserA?.subscriptionProductType)
          const weightB = getTypeWeight(advertiserB?.subscriptionProductType)
          
          return this.order === 'desc' ? weightB - weightA : weightA - weightB
        },
        render: (rowData) => {
          const handleClick = ( status?: string ) => {
            if (status === CircleStatus.CREATE_UNIT_NEEDED) {
              openToast(t('circle.toast.createUnitNeeded'))
              return
            }
            const path = `/setting/subscription/${rowData.advertiserId}`
            navigate(path, {
              state: {
                advertiserId: rowData.advertiserId,
                advertiserName: rowData.advertiserName
              }
            })
          }
          const currentAdvertiser = advertiserList?.find(adv => adv.advertiserId === rowData.advertiserId)
          const isPro = currentAdvertiser?.subscriptionProductType === SubscriptionProductType.PRO
          const isLite = currentAdvertiser?.subscriptionProductType === SubscriptionProductType.LITE
          const isBasic = currentAdvertiser?.subscriptionProductType === SubscriptionProductType.BASIC

          return (
            <div className="cell-body-box">
              {isPro && <PlanButton variant='pro' onClick={() => handleClick(rowData.status)} />}
              {isLite && <PlanButton variant='lite' onClick={() => handleClick(rowData.status) } />}
              {isBasic &&
                <ArrowRightButton
                  contained={false}
                  label={t('circle.label.button.subscribe')}
                  onClick={() => handleClick(rowData.status)}
                  gtmId="adcircle-subscribe"
                />}
            </div>
          )
        }
      }
    }

    const columnCircleName = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('circle.label.columnHeader.advertiserName')}
            children={
              <CommonTooltip
                id={`adCircle-info-tooltip`}
                title={
                  <>
                    <h1>{t('circle.tooltip.list.adCircle.title')}</h1>
                    <div className="common-style">
                      <p>{t('circle.tooltip.list.adCircle.content.0')}</p>
                      <p className="indent2">{t('circle.tooltip.list.adCircle.content.1')}</p>
                      <p className="indent2">{t('circle.tooltip.list.adCircle.content.2')}</p>
                      <p className="indent2">{t('circle.tooltip.list.adCircle.content.3')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'advertiserName',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'advertiserName' ? this.order : undefined,
        cellStyle: {
          width: '20%'
        },
        render: (rowData) => {
          const hasExistUnit = Object.values(rowData.unitCounts).some((unit) => unit !== 0)
          const handleClick = () => {
            const path = `./${rowData.advertiserId}${hasExistUnit ? '' : '/new-unit'}`
            navigate(path, {
              state: {
                advertiserId: rowData.advertiserId,
                advertiserName: rowData.advertiserName,
                currency: rowData.advertiserCurrencyCode
              }
            })
          }
          const openEditModal = (e: any) => {
            e.stopPropagation()
            this.handleEdit(rowData)
          }
          
          return (
            <div
              className="cell-body-box pointer"
              onClick={hasSystemViewerAuthority && !hasExistUnit ? () => {} : handleClick}
            >
              {rowData.advertiserName}
              {/* hasAuthority && */}
              {!hasSystemViewerAuthority && <EditIcon onClick={openEditModal} />}
            </div>
          )
        }
      }
    }

    const columnCurrency = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('circle.label.columnHeader.advertiserCurrencyCode')}
            children={
              <CommonTooltip
                id={`currency-info-tooltip`}
                title={
                  <>
                    <h1>{t('circle.tooltip.list.currency.title')}</h1>
                    <div className="common-style">
                      <p>{t('circle.tooltip.list.currency.content.0')}</p>
                      <p className="indent2">{t('circle.tooltip.list.currency.content.1')}</p>
                      <p className="indent2">
                        <InnerHtml innerHTML={t('circle.tooltip.list.currency.content.2')} />
                      </p>
                      <p>{t('circle.tooltip.list.currency.content.3')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'advertiserCurrencyCode',
        align: 'center',
        sorting: true,
        defaultSort: this.field === 'advertiserCurrencyCode' ? this.order : undefined,
        cellStyle: {
          width: '5%'
        },
        render: (rowData) => {
          return (
            <div className="cell-body-box">
              <TextIcon code={rowData.advertiserCurrencyCode} size={28} />
            </div>
          )
        }
      }
    }

    const columnUnit = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('circle.label.columnHeader.unitCounts')}
            children={
              <CommonTooltip
                id={`unit-info-tooltip`}
                title={
                  <>
                    <h1>{t('circle.tooltip.list.unit.title')}</h1>
                    <div className="common-style">
                      <p>{t('circle.tooltip.list.unit.content.0')}</p>
                      <p className="indent2">{t('circle.tooltip.list.unit.content.1')}</p>
                      <p className="indent2">{t('circle.tooltip.list.unit.content.2')}</p>
                      <h2>{t('circle.tooltip.list.unit.content.3.title')}</h2>
                      <ul className="indent2">
                        <li>{t('circle.tooltip.list.unit.content.3.list1')}</li>
                        <li>{t('circle.tooltip.list.unit.content.3.list2')}</li>
                        <li>{t('circle.tooltip.list.unit.content.3.list3')}</li>
                      </ul>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'unitCounts',
        align: 'center',
        defaultSort: this.field === 'unitCounts' ? this.order : undefined,
        customSort: (a, b) => {
          let sumA = 0
          let sumB = 0
          for (let unit in a.unitCounts) {
            sumA += Number(a.unitCounts[unit as keyof UnitCounts])
          }
          for (let unit in b.unitCounts) {
            sumB += Number(b.unitCounts[unit as keyof UnitCounts])
          }
          if (sumA < sumB) {
            return -1
          }
          if (sumA > sumB) {
            return 1
          }
          return 0
        },
        cellStyle: {
          width: '15%'
        },
        render: ({ advertiserId, advertiserName, unitCounts, advertiserCurrencyCode }) => {
          const hasExistUnit = Object.values(unitCounts).some((unit) => unit !== 0)
          const routeUnitSetting = () => {
            navigate(`./${advertiserId}${hasExistUnit ? '' : '/new-unit'}`, {
              state: { advertiserId, advertiserName, currency: advertiserCurrencyCode }
            })
          }
          const routeUnitDetail = (e: React.MouseEvent<HTMLDivElement>) => {
            const gtmEventId = e.currentTarget.dataset.gtmId
            TagManager.dataLayer({
              dataLayer: {
                event: 'click',
                gtm_id: gtmEventId
              }
            })
            routeUnitSetting()
          }
          const mediaUnits = {
            NAVER: unitCounts.naverCount,
            KAKAO: unitCounts.kakaoCount,
            GOOGLE: unitCounts.googleCount,
            META: unitCounts.metaCount,
            CRITEO: unitCounts.criteoCount
          }
          return (
            <div className="cell-body-box">
              {hasExistUnit ? (
                <div className="icon-wrapper" onClick={routeUnitDetail} role="button" data-gtm-id="adcircle-detail">
                  {Object.keys(mediaUnits).map((media) => (
                    <div className="text-icon-wrapper">
                      <MediaIcon mediaType={media} size={23} />
                      <span className="count-number">{mediaUnits[media as keyof typeof mediaUnits]}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <>
                  {
                    //  true // hasAuthority
                    //   // NOTE: 현재는 admin 만 테이블을 볼 수 있음, 추후에 operator 도 보일 예정
                    //   ? (
                    //     <ArrowRightButton
                    //       contained={false}
                    //       label={t('circle.label.button.unit')}
                    //       onClick={routeUnitSetting}
                    //     />
                    //   )
                    //   : <span className='setting--disable'>유닛 설정 필요</span>
                    hasSystemViewerAuthority ? (
                      // NOTE: systemviewer 일 경우에는 유닛설정 불가
                      <span className="setting--disable">유닛 설정 필요</span>
                    ) : (
                      <ArrowRightButton
                        contained={false}
                        label={t('circle.label.button.unit')}
                        onClick={routeUnitSetting}
                        gtmId="adcircle-new-unit"
                      />
                    )
                  }
                </>
              )}
            </div>
          )
        }
      }
    }

    const columnMemberAuthorities = (): TableRow => {
      return {
        title: (
          <TableTitle
            titleStr={t('circle.label.columnHeader.authCounts')}
            children={
              <CommonTooltip
                id={`auth-info-tooltip`}
                title={
                  <>
                    <h1>{t('circle.tooltip.list.auth.title')}</h1>
                    <div className="common-style">
                      <p>{t('circle.tooltip.list.auth.content.0')}</p>
                      <p className="indent2">
                        <InnerHtml innerHTML={t('circle.tooltip.list.auth.content.1')} />
                      </p>
                      <p>{t('circle.tooltip.list.auth.content.2')}</p>
                    </div>
                  </>
                }
                placement="right-start"
                arrow
              >
                <AdviceMarkIcon className="tooltip-icon" />
              </CommonTooltip>
            }
          />
        ),
        field: 'authCounts',
        align: 'center',
        defaultSort: this.field === 'authCounts' ? this.order : undefined,
        customSort: (a, b) => {
          let sumA = 0
          let sumB = 0
          for (let auth in a.authCounts) {
            sumA += Number(a.authCounts[auth as keyof AuthCounts])
          }
          for (let auth in b.authCounts) {
            sumB += Number(b.authCounts[auth as keyof AuthCounts])
          }
          if (sumA < sumB) {
            return -1
          }
          if (sumA > sumB) {
            return 1
          }
          return 0
        },
        cellStyle: {
          width: '10%'
        },
        render: ({ authCounts, advertiserId, advertiserName }) => {
          const hasMemberAuthority = authCounts !== null
          const { administrateCnt, operateCnt, readCnt } = authCounts
          const showAuthSetting = administrateCnt < 2 && !operateCnt && !readCnt
          // FIXME
          return (
            <div className="cell-body-box">
              {hasMemberAuthority ? (
                showAuthSetting ? (
                  <>
                    {
                      // NOTE: systemviewer 일 경우에는 권한 설정 불가
                      hasSystemViewerAuthority ? (
                        <span className="setting--disable">권한 설정 필요</span>
                      ) : (
                        <ArrowRightButton
                          contained={false}
                          label={t('circle.label.button.auth')}
                          onClick={() => this.handleAuthModal(advertiserId, advertiserName)}
                          gtmId="adcircle-auth-setting"
                        />
                      )
                    }
                  </>
                ) : (
                  <div className="icon-wrapper">
                    {Object.keys(authCounts).map((auth) => (
                      <div
                        className="text-icon-wrapper"
                        onClick={() => this.handleAuthModal(advertiserId, advertiserName)}
                      >
                        <TextIcon code={trimText(auth, 'Cnt')} size={23} />
                        <span className="count-number">{authCounts[auth as keyof AuthCounts]}</span>
                      </div>
                    ))}
                  </div>
                )
              ) : (
                <>
                  {
                    // true ? ( //hasAuthority
                    //   <ArrowRightButton
                    //     contained={false}
                    //     label={t('circle.label.button.auth')}
                    //     onClick={() => this.handleAuthModal(advertiserId, advertiserName)}
                    //   />
                    // ) : (
                    //   <span className="setting--disable">권한 설정 필요</span>
                    // )
                    // NOTE: systemviewer 일 경우에는 권한 설정 불가
                    hasSystemViewerAuthority ? (
                      <span className="setting--disable">권한 설정 필요</span>
                    ) : (
                      <ArrowRightButton
                        contained={false}
                        label={t('circle.label.button.auth')}
                        onClick={() => this.handleAuthModal(advertiserId, advertiserName)}
                      />
                    )
                  }
                </>
              )}
            </div>
          )
        }
      }
    }

    return [columnStatus(), columnId(), columnPlan(), columnCircleName(), columnCurrency(), columnUnit(), columnMemberAuthorities()]
  }
}
