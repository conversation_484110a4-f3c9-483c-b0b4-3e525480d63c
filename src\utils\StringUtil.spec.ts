import { toCamelCase, getOnlyCapitalizedFirstLetter, capitalizeFirstLetter } from './StringUtil'

describe('StringUtil', () => {
  describe('toCamelCase', () => {
    it('하이픈(-)으로 구분된 문자열을 카멜케이스로 변환한다', () => {
      expect(toCamelCase('hello-world')).toBe('helloWorld')
      expect(toCamelCase('hello-world-test')).toBe('helloWorldTest')
    })

    it('언더스코어(_)로 구분된 문자열을 카멜케이스로 변환한다', () => {
      expect(toCamelCase('hello_world')).toBe('helloWorld')
      expect(toCamelCase('hello_world_test')).toBe('helloWorldTest')
    })

    it('공백으로 구분된 문자열을 카멜케이스로 변환한다', () => {
      expect(toCamelCase('hello world')).toBe('helloWorld')
      expect(toCamelCase('hello world test')).toBe('helloWorldTest')
    })

    it('여러 구분자가 혼합된 문자열을 카멜케이스로 변환한다', () => {
      expect(toCamelCase('hello-world_test')).toBe('helloWorldTest')
      expect(toCamelCase('hello_world-test')).toBe('helloWorldTest')
    })

    it('대문자가 포함된 문자열을 소문자로 시작하는 카멜케이스로 변환한다', () => {
      expect(toCamelCase('Hello-World')).toBe('helloWorld')
      expect(toCamelCase('HELLO-WORLD')).toBe('helloWorld')
    })

    it('구분자가 없는 문자열은 소문자로 변환한다', () => {
      expect(toCamelCase('hello')).toBe('hello')
      expect(toCamelCase('HELLO')).toBe('hello')
    })
  })

  describe('getOnlyCapitalizedFirstLetter', () => {
    it('문자열의 첫 글자를 대문자로 변환하여 반환한다', () => {
      expect(getOnlyCapitalizedFirstLetter('hello')).toBe('H')
      expect(getOnlyCapitalizedFirstLetter('world')).toBe('W')
    })

    it('이미 대문자로 시작하는 문자열의 첫 글자를 반환한다', () => {
      expect(getOnlyCapitalizedFirstLetter('Hello')).toBe('H')
      expect(getOnlyCapitalizedFirstLetter('World')).toBe('W')
    })

    it('빈 문자열의 경우 빈 문자열을 반환한다', () => {
      expect(getOnlyCapitalizedFirstLetter('')).toBe('')
    })
  })

  describe('capitalizeFirstLetter', () => {
    it('문자열의 첫 글자를 대문자로 변환하고 나머지는 그대로 반환한다', () => {
      expect(capitalizeFirstLetter('hello')).toBe('Hello')
      expect(capitalizeFirstLetter('world')).toBe('World')
    })

    it('이미 대문자로 시작하는 문자열은 그대로 반환한다', () => {
      expect(capitalizeFirstLetter('Hello')).toBe('Hello')
      expect(capitalizeFirstLetter('World')).toBe('World')
    })

    it('빈 문자열의 경우 빈 문자열을 반환한다', () => {
      expect(capitalizeFirstLetter('')).toBe('')
    })

    it('한 글자 문자열의 경우 대문자로 변환하여 반환한다', () => {
      expect(capitalizeFirstLetter('h')).toBe('H')
      expect(capitalizeFirstLetter('w')).toBe('W')
    })
  })
})
