/* istanbul ignore file */
import { callApi, Method } from '@utils/ApiUtil';
import { Service } from '@models/common/Service';
import * as oauthModel from '@models/oauth/Oauth';
import { getRedirectBaseURL } from '@pages/setting/CallbackPage';
import { MediaType, MopMedia } from '@models/common';
import CommonResponse from '@models/common/CommonResponse';

export const oauthLoginURLForGoogleAds = async (
  request: oauthModel.GoogleAdsRedirectRequest,
  provider: string,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/oauth/redirect`,
    method: Method.GET,
    params: {
      queryParams: {
        ...request,
        provider: provider,
        redirectBaseURL: getRedirectBaseURL(provider as string),
      },
    },
    config: { isLoading: isLoading },
  });
};

export const oauthLoginURL = async (provider: string, data?: Record<string, any> | null, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/oauth/redirect`,
    method: Method.GET,
    params: {
      queryParams: {
        provider,
        ...(data && data),
        redirectBaseURL: getRedirectBaseURL(provider as string),
      },
    },
    config: { isLoading: isLoading },
  });
};

export const oauthAccessToken = async (request: oauthModel.CallbackRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/setting/oauth/callback',
    method: Method.POST,
    params: { bodyParams: request },
    config: { isLoading: isLoading },
  });
};

export const oauthAccountList = async (email: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/oauth/accounts',
    method: Method.GET,
    params: {
      queryParams: {
        email: email,
      },
    },
    config: { isLoading: isLoading },
  });
};

export const oauthMediaAccountList = async (email: string, media: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/accounts/${media}`,
    method: Method.GET,
    params: {
      queryParams: {
        email: email,
      },
    },
    config: { isLoading: isLoading },
  });
};

export const oauthEnrollNaverInfo = async (request: oauthModel.NaverRequest, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/setting/naver',
    method: Method.POST,
    params: {
      bodyParams: request,
    },
    config: { isLoading: isLoading },
  });
};

export const connectOAuthAccount = async (
  media: MopMedia,
  request: oauthModel.ConnectOAuthRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/${media.toLocaleLowerCase()}`,
    method: Method.POST,
    params: {
      bodyParams: request,
    },
    config: { isLoading: isLoading },
  });
};

export const updateOauthAPI = async ({ media, apiParams }: oauthModel.UpdateOauthAPIParams, isLoading = true) => {
  const path = media === MediaType.NAVER ? 'naver/api-key' : 'airbridge/api-token';

  const response = await callApi({
    service: Service.MOP_BE,
    url: `/v1/oauth/${path}`,
    method: Method.PATCH,
    params: {
      bodyParams: apiParams,
    },
    config: {
      isLoading: isLoading,
    },
  });
  return response;
};

export const searchAnalyticsMetricConfig = async (
  accountId: string,
  analyticsType: string,
  viewId?: string,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/oauth/metrics',
    method: Method.GET,
    params: {
      queryParams: {
        accountId,
        analyticsType,
        viewId: viewId ?? '',
      },
    },
    config: { isLoading: isLoading },
  });
};

export const enrollAnalyticsMetricConfig = async (
  authId: number,
  list: oauthModel.AnalyticsMetric[],
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: '/v1/oauth/metrics',
    method: Method.POST,
    params: {
      bodyParams: { authId, list },
    },
    config: { isLoading: isLoading },
  });
};

export const oauthGa4EventList = async (propertyId: string, isLoading = true) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/oauth/ga4/events/${propertyId}`,
    method: Method.GET,
    config: { isLoading: isLoading },
  });
};

export const updateGa4EventConfigs = async (
  authId: number,
  propertyId: string,
  eventIds: string[],
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/oauth/ga4/events/${propertyId}/configurations`,
    method: Method.PATCH,
    params: {
      bodyParams: {
        authId: authId,
        eventIds: eventIds,
      },
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const updateAccountUseYn = async (
  media: oauthModel.OauthMediaType,
  bodyParams: oauthModel.UpdateAccountUseYn,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/accounts/${media}`,
    method: Method.PATCH,
    params: {
      bodyParams,
    },
    config: {
      isLoading: isLoading,
    },
  });
};

export const getOAuthList = async (isLoading = true) => {
  const response: CommonResponse<oauthModel.OauthListItem[]> = await callApi({
    service: Service.MOP_BE,
    url: `/v1/setting/oauth/account`,
    method: Method.GET,
    config: {
      isLoading: isLoading,
    },
  });

  return response.successOrNot === 'Y' ? response.data : null;
};

// NOTE: 후에 google 외에 다른 미디어에서도 사용될 경우, 함수명 변경해야 할 수 있음
export const requestGoogleConnect = async (
  media: MopMedia,
  request: oauthModel.GoogleConnectRequest,
  isLoading = true
) => {
  return callApi({
    service: Service.MOP_BE,
    url: `/v1/oauth/request/${media}`,
    method: Method.POST,
    params: { bodyParams: request },
    config: { isLoading },
  });
};
