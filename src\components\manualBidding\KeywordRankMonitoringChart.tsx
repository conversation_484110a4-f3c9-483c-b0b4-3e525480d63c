import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react'
import './KeywordRankMonitoringChart.scss'
import { FormLabel, Grid, Switch, Box } from '@material-ui/core'
import { t } from 'i18next'
import { ToggleButton, ToggleButtonGroup } from '@material-ui/lab'
import {
  Chart as ChartJS,
  LinearScale,
  registerables,
  CategoryScale,
  BarElement,
  PointElement,
  LineController,
  LineElement,
  Legend,
  Tooltip
} from 'chart.js'
import { Chart } from 'react-chartjs-2'
import {
  ChartDataSet,
  ChartDataSets,
  KeywordRankMonitoringReportRequest,
  ShoppingRankMonitoringChartResult
} from '@models/rankMaintenance/KeywordRankMonitoring'
import {
  getChartTypeKeywordRankMonitorings,
  getKeywordRankMonitoringCompetitors
} from '@api/manualBidding/RankMonitoring'
import { ReactComponent as SettingIcon } from '@components/assets/images/icon_setting.svg'
import { ReactComponent as RankBidIcon } from '@components/assets/images/icon_rank_bid.svg'
import { useRecoilState, useRecoilValue } from 'recoil'
import { SScompetitorsState } from '@store/KeywordRankMonitoring'
import { cloneDeep } from 'lodash'
import KeywordCompetitorSettingModal from './KeywordCompetitorSettingModal'
import { advertiserState } from '@store/Advertiser'
import { AuthorityType } from '@models/common/Advertiser'
import { checkAuthority } from '@utils/AuthorityUtil'
import CustomTooltip from '@components/common/CustomTooltip'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'

import {
  ssChartOptions,
  externalTooltipHandler,
  reorderChips,
  convertChartData,
  getEvenRank
} from '@utils/RankMaintenanceUtils'

export interface Props {
  monitoringId: number
  startDate: string
  endDate: string
}

const TIME_INTERVAL = 2
const TIME_ARRAY_NUM = 24 / TIME_INTERVAL
const NOON_INDEX = TIME_ARRAY_NUM / 2

const defaultChipColors = ['#040A45', '#4373B9', '#838DA4']
const competitorChipColors = ['#B41A3E', '#EB5E24', '#F3AA24', '#9E569E', '#04746A']
const labels = [...Array(TIME_ARRAY_NUM).keys()].map((index) => '')
const defaultChartDataSets = { labels, datasets: [] }
const convertZeroToNull = (ranks: number[]) => {
  const convertedArray: (number | null)[] = ranks

  for (let i = 0; i < convertedArray.length; i++) {
    if (convertedArray[i] === 0) {
      convertedArray[i] = null
    }
  }
  return convertedArray
}

const KeywordRankMonitoringChart: React.FC<Props> = (props: Props): ReactElement => {
  const [defaultChips, setDefaultChips] = useState<string[]>([
    t(`common.code.chartDataType.CURRENT_RANK`),
    t(`common.code.chartDataType.TARGET_RANK`),
    t(`common.code.chartDataType.BID_AMOUNT`)
  ])
  const [competitorChips, setCompetitorChips] = useState<string[]>([])
  const [selectedCompetitors, setSelectedCompetitors] = useState<string[]>([])
  const [competitorEnable, setCompetitorEnable] = useState<boolean>(false)
  const [chartDataSets, setChartDataSets] = useState<ChartDataSets>(defaultChartDataSets)
  const [rankData, setRankData] = useState<ShoppingRankMonitoringChartResult>()
  const [showCompetitorModal, setShowCompetitorModal] = useState<boolean>(false)

  const [competitors, setCompetitors] = useRecoilState(SScompetitorsState)

  const chartRef = useRef<ChartJS>(null)
  const advertiser = useRecoilValue(advertiserState)

  ChartJS.register(...registerables)
  ChartJS.register(LineController, LinearScale, CategoryScale, BarElement, PointElement, LineElement, Legend, Tooltip)

  const hasDataOfCompetitor = (competitor: string) => {
    if (rankData && rankData.competitors) {
      for (const c of rankData.competitors) {
        if (c.alias === competitor) {
          return true
        }
      }
    }

    return false
  }

  const [chartOptions, setChartOptions] = useState<any>(ssChartOptions(externalTooltipHandler, chartRef))

  const handleDefaultChipClick = (event: React.MouseEvent<HTMLElement>, selectedChips: string[]) => {
    const { current: chart } = chartRef

    if (chart) {
      for (const d of chart.data.datasets) {
        d.hidden = true
      }
      let bidAmountSelected = false
      for (const selectedChip of selectedChips) {
        if (selectedChip === t(`common.code.chartDataType.BID_AMOUNT`)) {
          bidAmountSelected = true
        }
        for (const dataset of chart.data.datasets) {
          if (dataset.label === selectedChip) {
            dataset.hidden = false
          }
        }
      }
      for (const competitorChip of selectedCompetitors) {
        for (const dataset of chart.data.datasets) {
          if (dataset.label === competitorChip) {
            dataset.hidden = false
          }
        }
      }

      const newBid = genAmountData(chartDataSets, bidAmountSelected, rankData && rankData.bidAmounts)
      setChartDataSets(newBid)

      chart.update('normal')
    }
    setDefaultChips(selectedChips)
  }

  const handleCompetitorChipClick = (event: React.MouseEvent<HTMLElement>, selectedChips: string[]) => {
    const { current: chart } = chartRef

    if (competitorEnable) {
      if (chart) {
        for (const d of chart.data.datasets) {
          d.hidden = true
        }
        for (const c of defaultChips) {
          for (const d of chart.data.datasets) {
            if (d.label === c) {
              d.hidden = false
            }
          }
        }
        for (const c of selectedChips) {
          let dataExist = false
          for (const d of chart.data.datasets) {
            if (d.label === c) {
              d.hidden = false
              dataExist = true
            }
          }
          if (!dataExist) {
            addCompetitorChartData(c)
          }
        }

        chart.update('normal')
      }

      setSelectedCompetitors(selectedChips)
    }
  }

  const getChartTypeData = async (param: KeywordRankMonitoringReportRequest) => {
    if (param.monitoringId === 0) {
      setSelectedCompetitors([])
      deleteAllChartData()
      return
    }
    const response = await getChartTypeKeywordRankMonitorings(param)
    let chartData = response
    if (Object.keys(response).length === 0) {
      chartData = []
    }
    const convertedChartData = convertChartData(chartData)
    setRankData(convertedChartData)
    addMyChartData(
      convertedChartData,
      chartData.map((item) => item.date)
    )
  }

  const genAmountData = useCallback((dataSet: ChartDataSets, selected: boolean, bidAmount?: number[]) => {
    const newChartDataSets = cloneDeep(dataSet) as ChartDataSets
    const bidAmountDataSet = newChartDataSets.datasets.pop()

    if (bidAmountDataSet) {
      if (bidAmountDataSet.label === t(`common.code.chartDataType.BID_AMOUNT`)) {
        bidAmountDataSet.data = selected && bidAmount ? convertZeroToNull(bidAmount) : ([] as number[])
      }
      newChartDataSets.datasets.push(bidAmountDataSet)
    }
    return newChartDataSets
  }, [])

  const addCompetitorChartData = (dataLabel: string) => {
    const competitorIndex = rankData?.competitors?.findIndex((competitor) => competitor.alias === dataLabel) ?? -1

    if (rankData && rankData.competitors && competitorIndex >= 0) {
      const newChartDataSets = cloneDeep(chartDataSets)

      const dataSet = {
        type: 'line' as const,
        label: dataLabel,
        borderColor: competitorChipColors[competitorIndex],
        backgroundColor: competitorChipColors[competitorIndex],
        borderWidth: 2,
        fill: false,
        data: convertZeroToNull(rankData.competitors[competitorIndex].ranks),
        yAxisID: 'rankY'
      }

      const bidAmountDataSet: ChartDataSet | undefined = newChartDataSets?.datasets.pop()

      newChartDataSets.datasets.push(dataSet)
      bidAmountDataSet && newChartDataSets.datasets.push(bidAmountDataSet)

      setChartDataSets(newChartDataSets)
    }
  }

  const deleteAllChartData = () => {
    const newChartDataSets = cloneDeep(chartDataSets)
    newChartDataSets.datasets = []
    newChartDataSets.labels = labels
    setChartDataSets(newChartDataSets)
  }

  const addMyChartData = (chartData: ShoppingRankMonitoringChartResult, chartDateArray: string[]) => {
    const newChartDataSets = cloneDeep(chartDataSets)
    const newChartOptions = cloneDeep(chartOptions)

    newChartDataSets.datasets = [
      {
        type: 'line' as const,
        label: t('common.code.chartDataType.CURRENT_RANK'),
        borderColor: defaultChipColors[0],
        backgroundColor: defaultChipColors[0],
        borderWidth: 2,
        fill: false,
        data: [] as number[],
        yAxisID: 'rankY'
      },
      {
        type: 'line' as const,
        label: t('common.code.chartDataType.TARGET_RANK'),
        borderColor: defaultChipColors[1],
        backgroundColor: defaultChipColors[1],
        borderWidth: 2,
        fill: false,
        data: [] as number[],
        yAxisID: 'rankY'
      },
      {
        type: 'bar' as const,
        label: t('common.code.chartDataType.BID_AMOUNT'),
        backgroundColor: defaultChipColors[2],
        data: [] as number[],
        barPercentage: 0.2,
        borderRadius: 5,
        yAxisID: 'bidAmountY'
      }
    ]

    newChartDataSets.datasets[0].data = convertZeroToNull(chartData.ranks)
    newChartDataSets.datasets[1].data = convertZeroToNull(chartData.targetRanks)
    newChartDataSets.datasets[2].data = convertZeroToNull(chartData.bidAmounts)

    if (chartData.bidAmounts.length > 0) {
      newChartOptions.scales.bidAmountY.max = Math.max.apply(null, chartData.bidAmounts)
    }

    let newLabels: (string | string[])[] = []
    if (chartDateArray.length !== 0) {
      chartDateArray.forEach((chartData) => {
        newLabels = newLabels.concat(
          [...Array(24).keys()].map((index) => {
            const hourPostfix: string = index >= 12 ? 'PM' : 'AM'
            if (index === 0 || index === 12) {
              return [chartData, '12', hourPostfix]
            }

            return [chartData, String(index > 12 ? index - 12 : index), hourPostfix]
          })
        )
      })
      newLabels.push([''])
    } else {
      newLabels = labels
    }
    newChartDataSets.labels = getEvenRank(newLabels)

    setChartOptions(newChartOptions)
    setChartDataSets(newChartDataSets)
  }

  const handleSettingClick = () => {
    setShowCompetitorModal(true)
  }

  const handleCompetitorEnableChange = (event: any) => {
    setCompetitorEnable(event.target.checked as boolean)

    const { current: chart } = chartRef
    if (event.target.checked === false) {
      setSelectedCompetitors([])

      if (chart) {
        chart.data.datasets.forEach((d: any) => {
          d.hidden = true
        })
        for (const c of defaultChips) {
          for (const d of chart.data.datasets) {
            if (d.label === c) {
              d.hidden = false
            }
          }
        }

        chart.update('normal')
      }
    }
  }

  const handleSettingModalClose = (saveYn: string) => {
    setShowCompetitorModal(false)

    if (saveYn === 'Y') {
      setCompetitors([])
      setSelectedCompetitors([])
      deleteAllChartData()

      getKeywordRankMonitoringCompetitors(props.monitoringId).then((response) => {
        setCompetitors(response)
      })

      getChartTypeData(props)
    }
  }

  useEffect(() => {
    if (rankData && rankData.competitors) {
      setCompetitorChips(reorderChips(competitorChips, rankData.competitors))
    }
  }, [rankData]) //eslint-disable-line

  useEffect(() => {
    setSelectedCompetitors([])
    deleteAllChartData()
    setDefaultChips([
      t(`common.code.chartDataType.CURRENT_RANK`),
      t(`common.code.chartDataType.TARGET_RANK`),
      t(`common.code.chartDataType.BID_AMOUNT`)
    ])

    getChartTypeData(props)
  }, [props]) //eslint-disable-line

  useEffect(() => {
    const chips: string[] = []
    if (competitors) {
      for (const c of competitors) {
        if (c.alias && c.monitoringYn === 'Y') {
          chips.push(c.alias)
        }
      }
      if (rankData && rankData.competitors) {
        setCompetitorChips(reorderChips(chips, rankData.competitors))
      } else {
        setCompetitorChips(chips)
      }
    }
  }, [competitors]) //eslint-disable-line

  return (
    <div id="KeywordRankMonitoringChart">
      <Grid container className="chart-label-container">
        <Grid item>
          <Box className="chart-label chart-view-onoff">
            <ModalTooltip id="view-on-off" field="ranking" color="white" />
            <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.chart.label.view')}</FormLabel>
          </Box>
        </Grid>
        <Grid container>
          <Grid item>
            <Box className={`chart-label chart-competitor-onoff chart-competitor-${competitorChips.length}`}>
              <ModalTooltip id="competitor-on-off" field="ranking" color="white" />
              <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.chart.label.competitor')}</FormLabel>
            </Box>
          </Grid>
          <Grid item>
            <Box className="chart-label chart-monitoring-onoff">
              <ModalTooltip id="monitoring" field="ranking" color="white" />
              <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.chart.label.monitoring')}</FormLabel>
            </Box>
          </Grid>
          {checkAuthority(AuthorityType.OPERATE, advertiser.authorityType) && (
            <Grid item>
              <Box className="chart-label chart-setting">
                <ModalTooltip id="setting" field="ranking" color="white" />
                <FormLabel>{t('rankMaintenance.label.RankMonitoringModal.chart.label.setting')}</FormLabel>
              </Box>
            </Grid>
          )}
        </Grid>
      </Grid>
      <Grid container className="chart-input-container">
        <Grid item className="chart-input chart-view-onoff">
          <ToggleButtonGroup value={defaultChips} onChange={handleDefaultChipClick}>
            <ToggleButton
              className="chart-onoff-button chart-onoff-button-current"
              data-testid={`currentRankChip`}
              value={t(`common.code.chartDataType.CURRENT_RANK`)}
            >
              {t(`common.code.chartDataType.CURRENT_RANK`)}
            </ToggleButton>
            <ToggleButton
              className="chart-onoff-button chart-onoff-button-target"
              data-testid={`targetRankChip`}
              value={t(`common.code.chartDataType.TARGET_RANK`)}
            >
              {t(`common.code.chartDataType.TARGET_RANK`)}
            </ToggleButton>
            <ToggleButton
              className="chart-onoff-button chart-onoff-button-bidAmount"
              data-testid={`bidAmountChip`}
              value={t(`common.code.chartDataType.BID_AMOUNT`)}
            >
              <RankBidIcon />
              {t(`common.code.chartDataType.BID_AMOUNT`)}
            </ToggleButton>
          </ToggleButtonGroup>
        </Grid>
        <Grid container>
          {competitorChips.length > 0 && (
            <Grid item className={`chart-input chart-competitor-onoff chart-competitor-${competitorChips.length}`}>
              <ToggleButtonGroup value={selectedCompetitors} onChange={handleCompetitorChipClick}>
                {competitorChips.map((competitor, index) => {
                  return (
                    <ToggleButton
                      key={`competitorChip-${index}`}
                      className="chart-competitor-onoff-button"
                      data-testid={`competitorChip-${index}`}
                      value={competitor}
                      disabled={!competitorEnable || !hasDataOfCompetitor(competitor)}
                    >
                      <CustomTooltip title={competitor} placement="bottom">
                        <span>{competitor}</span>
                      </CustomTooltip>
                    </ToggleButton>
                  )
                })}
              </ToggleButtonGroup>
            </Grid>
          )}
          <Grid item className="chart-input chart-monitoring-onoff">
            <Switch
              data-testid={`competitorEnableSwitch`}
              checked={competitorEnable}
              onChange={handleCompetitorEnableChange}
              disabled={rankData && rankData.competitors && rankData.competitors.length > 0 ? false : true}
            />
          </Grid>
          {checkAuthority(AuthorityType.OPERATE, advertiser.authorityType) && (
            <Grid item className="chart-input chart-setting">
              {checkAuthority(AuthorityType.OPERATE, advertiser.authorityType) && (
                <span>
                  <SettingIcon data-testid={`settingIcon`} onClick={handleSettingClick} />
                </span>
              )}
            </Grid>
          )}
        </Grid>
      </Grid>
      {chartDataSets && (
        <div id="keyword-rank-monitoring-chart">
          <div className="chart-y-axis-title">
            <span>{t('rankMaintenance.label.RankMonitoringModal.chart.label.yAxisRanking')}</span>{' '}
            <span>{t('rankMaintenance.label.RankMonitoringModal.chart.label.yAxisBidCpc')}</span>
          </div>
          <Chart
            data-testid={`keywordRankMonitoringChart`}
            ref={chartRef}
            type="line"
            height={115}
            options={chartOptions}
            data={chartDataSets as any}
          />
        </div>
      )}
      {showCompetitorModal && (
        <KeywordCompetitorSettingModal
          open={showCompetitorModal}
          monitoringId={props.monitoringId}
          onClose={(saveYn) => handleSettingModalClose(saveYn)}
        />
      )}
    </div>
  )
}

export default KeywordRankMonitoringChart
