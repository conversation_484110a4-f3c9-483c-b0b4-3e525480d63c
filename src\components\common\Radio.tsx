import React, { ReactNode } from 'react'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { cn } from '@utils/index'

const Label = tw.label`flex items-center cursor-pointer`
const RadioWrapper = styled.div<{ size: number }>`
  ${tw`relative`};
  ${({ size = 16 }) => `width: ${size}px; height: ${size}px`};
`
const RadioInput = tw.input`absolute inset-0 w-full h-full cursor-pointer appearance-none rounded-full border border-slate-300 checked:border-slate-400 transition-all`
const RadioDot = tw.span`absolute w-3/5 h-3/5 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full opacity-0  transition-opacity duration-200 transform`

interface Props extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: ReactNode | string
  size?: number
  color?: string
}

const Radio = ({ name, label, size = 16, color, ...restProps }: Props) => {
  return (
    <Label className={`${name}-label`} htmlFor={name}>
      <RadioWrapper size={size}>
        <RadioInput id={name} type="radio" {...restProps} className="peer" />
        <RadioDot className={cn(color ?? 'bg-status-active', 'peer-checked:opacity-100')} />
      </RadioWrapper>
      {label}
    </Label>
  )
}

export default Radio
