import React, { ReactElement, useEffect, useRef, useState, useMemo } from 'react'
import { useRecoilState, useRecoilValue, useResetRecoilState } from 'recoil'
import { Dialog } from '@material-ui/core'
import { RankMaintenanceTarget } from '@components/manualBidding'

import { CreateManualBidParams, ManualBidSetting, ManualBidSettingItem } from '@models/manualBidding'
import { createManualBidItem, getManualBidDetailById, updateManualBidItem } from '@api/manualBidding'
import { useToast, useDialog, useActionType, useAuthority } from '@hooks/common'

import ShoppingBidKeywordSetting from './ShoppingBidKeywordSetting'
import { objectCompare } from '@utils/CompareUtil'
import {
  shoppingBidSettingState,
  TargetBidSettingDefault,
  originBidTargetSettingState,
  shoppingBidSettingSelector
} from '@store/SsRankMaintenance'
import { object, string, number, ref } from 'yup'
import { t } from 'i18next'
import './RankMaintenanceModal.scss'

import ModalTitle from '@components/common/optimization/ModalTitle'
import { ReactComponent as TitleLabelIcon } from '@components/assets/images/icon_label_SA.svg'
import { MopIcon, TransButton } from '@components/common'
import { ActionType, StatusCode, ButtonUI, MOPIcon, YNFlag } from '@models/common'
import { isSuccessResponse } from '@models/common/CommonResponse'

interface Props {
  onClose: (saveYn: string) => void
  monitoringId: number | null
  modalType: ActionType
  open: boolean
  reload: () => void
}

const ShoppingBidSettingModal: React.FC<Props> = ({
  onClose,
  reload,
  open,
  monitoringId,
  modalType
}: Props): ReactElement => {
  const { advertiser, hasAuthority } = useAuthority()
  const [originSetting, setOriginSetting] = useRecoilState(originBidTargetSettingState)
  const [bidSettingInfo, setBidsettingInfo] = useRecoilState(shoppingBidSettingState)
  const searchKeywords = useRecoilValue(shoppingBidSettingSelector)
  const resetBidsettingInfo = useResetRecoilState(shoppingBidSettingState)

  const { isReadType, isEditType, isCreateType } = useActionType(modalType)

  const { openToast } = useToast()
  const { openDialog } = useDialog()

  const updateValidationSchema = object().shape({
    bidCriteria: string().required(t('common.message.validation.required', { param: t('common.label.bidCriteria') })),
    bidTarget: number()
      .required()
      .when('bidCriteria', {
        is: 'AVG_RANK',
        then: (schema) =>
          schema
            .min(1, t('rankMaintenance.validation.avgRankRange'))
            .max(10, t('rankMaintenance.validation.avgRankRange'))
      })
      .when('bidCriteria', {
        is: 'ROAS',
        then: (schema) => schema.positive(t('rankMaintenance.validation.roasPositive'))
      })
      .when('bidCriteria', {
        is: 'CPA',
        then: (schema) => schema.positive(t('rankMaintenance.validation.cpaPositive'))
      }),
    maxCpc: number()
      .required()
      .min(70, t('rankMaintenance.validation.maxCpcRange'))
      .max(100000, t('rankMaintenance.validation.maxCpcRange'))
      .test('is-multiple-of-10', t('rankMaintenance.validation.maxCpcUnit'), (value) => {
        if (!value) return false
        return value % 10 === 0
      }),
    incrementalValue: number().when('incrementalUseYn', {
      is: YNFlag.Y,
      then: (schema) =>
        schema
          .required()
          .min(10, t('rankMaintenance.validation.incrementalRange'))
          .max(ref('maxCpc'), t('rankMaintenance.validation.incrementalRange'))
          .test('is-multiple-of-10', t('rankMaintenance.validation.incrementalUnit'), (value) => {
            if (!value) return false
            return value % 10 === 0
          })
    })
  })
  const createValidationSchema = object()
    .shape({
      adId: string().required(t('common.message.validation.required', { param: t('common.label.adId') })),
      searchKeyword: string().when('$hasData', {
        is: true,
        then: (schema) =>
          schema.required(t('common.message.validation.required', { param: t('common.label.keyword') })),
        otherwise: (schema) => schema
      })
    })
    .concat(updateValidationSchema)

  const createShoppingBidSetting = async () => {
    const createParam: CreateManualBidParams = {
      advertiserId: bidSettingInfo.advertiserId,
      mediaType: bidSettingInfo.mediaType,
      searchKeyword: bidSettingInfo.searchKeyword,
      totalKeywordYn: bidSettingInfo.totalKeywordYn,
      adId: bidSettingInfo.adId,
      bidCriteria: bidSettingInfo.bidCriteria,
      bidTarget: bidSettingInfo.bidTarget,
      incrementalUseYn: bidSettingInfo.incrementalUseYn,
      incrementalValue: bidSettingInfo.incrementalValue,
      maxCpc: bidSettingInfo.maxCpc
    }
    const response = await createManualBidItem(createParam)
    switch (response.statusCode) {
      case StatusCode.SUCCESS:
        openToast(t('common.message.saveSuccess'))
        reload()
        onClose('Y')
        break
      case StatusCode.DUPLICATE_AD_CONFIG:
        openToast(t('rankMaintenance.message.RankMaintenanceModal.duplicateAdId'))
        break
      case StatusCode.EXCEED_MAX_KEYWORD_CONFIG:
        openToast(t('rankMaintenance.message.RankMaintenanceModal.exceedMaxAdId'))
        break
      default:
        openToast(t('common.message.systemError'))
        break
    }
  }

  const updateShoppingBidSetting = async (body: ManualBidSetting) => {
    if (!bidSettingInfo.adMonitoringId) return
    const response = await updateManualBidItem(bidSettingInfo.adMonitoringId, body)
    if (response.statusCode === StatusCode.SUCCESS) {
      openToast(t('common.message.saveSuccess'))
      onClose('Y')
    } else {
      openToast(t('common.message.systemError'))
    }
  }

  const handleSave = () => {
    const bidTargetSetting = {
      bidCriteria: bidSettingInfo.bidCriteria,
      bidTarget: bidSettingInfo.bidTarget,
      incrementalUseYn: bidSettingInfo.incrementalUseYn,
      incrementalValue: bidSettingInfo.incrementalValue,
      maxCpc: bidSettingInfo.maxCpc
    }
    if (modalType === ActionType.MODIFY && objectCompare(bidTargetSetting, originSetting)) {
      openToast(t('common.message.notChanged'))
      onClose('N')
      return
    }

    if (modalType === ActionType.CREATE) {
      createValidationSchema
        .validate(bidSettingInfo, {
          context: { hasData: searchKeywords.length > 0 }
        })
        .then(() => {
          openDialog({
            message: t('common.message.saveConfirm'),
            cancelLabel: t('rankMaintenance.label.RankMaintenanceModal.dialog.cancel'),
            actionLabel: t('rankMaintenance.label.RankMaintenanceModal.dialog.confirm'),
            onAction: createShoppingBidSetting
          })
        })
        .catch((err) => {
          openToast(err.message)
        })
    } else {
      updateValidationSchema
        .validate(bidSettingInfo)
        .then(() => {
          openDialog({
            message: t('common.message.saveConfirm'),
            cancelLabel: t('rankMaintenance.label.RankMaintenanceModal.dialog.cancel'),
            actionLabel: t('rankMaintenance.label.RankMaintenanceModal.dialog.confirm'),
            onAction: () => {
              updateShoppingBidSetting(bidTargetSetting)
            }
          })
        })
        .catch((err) => {
          openToast(err.message)
        })
    }
  }

  const initInfo = async () => {
    if (isCreateType) {
      setBidsettingInfo({ ...TargetBidSettingDefault, advertiserId: advertiser.advertiserId })
    } else if (monitoringId) {
      const response = await getManualBidDetailById(monitoringId)
      if (isSuccessResponse(response)) {
        setBidsettingInfo(response.data)
        setOriginSetting({
          bidCriteria: response.data.bidCriteria,
          bidTarget: response.data.bidTarget,
          incrementalUseYn: response.data.incrementalUseYn,
          incrementalValue: response.data.incrementalValue,
          maxCpc: response.data.maxCpc
        })
      } else {
        openToast(t('common.message.systemError'))
        onClose('N')
      }
    }
  }

  const resetBidsetting = () => {
    if (isCreateType) resetBidsettingInfo()
    else {
      setBidsettingInfo((prev) => ({
        ...prev,
        ...originSetting
      }))
    }
  }

  useEffect(() => {
    initInfo()
    return () => {
      resetBidsettingInfo()
    }
  }, []) // eslint-disable-line

  return (
    <Dialog open={open} onClose={() => onClose('N')} PaperProps={{ style: { minWidth: '1560px', borderRadius: 0 } }}>
      <section className="flex flex-col items-center bg-gray-light px-6 py-5">
        <div className="flex w-full">
          <ModalTitle type={modalType} title="Shopping Ad" icon={<TitleLabelIcon width={24} />} />
          <MopIcon
            customClass="absolute inset-y-4 right-3"
            name={MOPIcon.CLOSE}
            onClick={() => onClose('N')}
            size={20}
            bgColor="#f3f3f6"
          />
        </div>
        {bidSettingInfo && <RankMaintenanceTarget modalType={modalType} />}
      </section>
      <ShoppingBidKeywordSetting modalType={modalType} />
      <section className="flex flex-col w-full px-6 pb-2 gap-4">
        {hasAuthority && !isReadType && (
          <div className="w-full flex items-center justify-end gap-4">
            <TransButton
              className="px-10 rounded-full"
              ui={ButtonUI.Contained}
              onClick={resetBidsetting}
              i18nKey={`common.button.reset-e`}
            />
            <TransButton
              className="px-10 rounded-full"
              ui={ButtonUI.Contained}
              onClick={handleSave}
              i18nKey={`common.button.save-e`}
            />
          </div>
        )}
      </section>
    </Dialog>
  )
}

export default ShoppingBidSettingModal
