import tw from 'twin.macro'
import styled from '@emotion/styled'
import { TransText } from '@components/common'
import { ReactComponent as FlowPC } from '@assets/images/intro/img-our-technology-pc.svg'
import { ReactComponent as FlowMobile } from '@assets/images/intro/img-our-technology-mo.svg'

const OurTechnology = () => {
  return (
    <div className='text-center flex flex-col gap-10'>
      <div>
        <TransText as='h2' className='text-landing-red text-xl font-medium' i18nKey='landing.ourTechnology.title' />
        <TransText as='h3' className='mt-14 text-white text-4xl font-light leading-normal' i18nKey='landing.ourTechnology.subTitle' components={{ bold: <span className='font-bold' /> }} />
        <TransText as='p' className='mt-2.5 text-white text-base font-light leading-relaxed' i18nKey='landing.ourTechnology.desc.0' />
      </div>
      <div>
        <FlowPC className="hidden md:w-full md:block lg:w-auto mx-auto" />
        <FlowMobile className="block md:hidden mx-auto" />
      </div>
      <div className='flex flex-col gap-7'>
        <TransText as='p' className='text-white text-base font-light leading-relaxed' i18nKey='landing.ourTechnology.desc.1' />
        <TransText as='p' className='text-white text-base font-light leading-relaxed' i18nKey='landing.ourTechnology.desc.2' />
        <TransText as='p' className='text-white text-base font-light leading-relaxed' i18nKey='landing.ourTechnology.desc.3' />
      </div>
    </div>
  )
}

export default OurTechnology
