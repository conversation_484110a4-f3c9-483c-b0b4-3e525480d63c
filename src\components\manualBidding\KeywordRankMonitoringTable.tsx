import React, { Fragment, ReactElement, useState, useEffect } from 'react'
import { TableBody, TableCell, TableRow, Table, TableHead } from '@material-ui/core'
import { t } from 'i18next'
import './KeywordRankMonitoringTable.scss'
import { KeywordRankMonitoringTablePerDate } from '@models/rankMaintenance/KeywordRankMonitoring'
import { getTableTypeKeywordRankMonitorings } from '@api/manualBidding/RankMonitoring'
import { format } from 'date-fns'
import { DateFnsFormat } from '@models/common/CommonConstants'
import { makeHoursLable, genEmptyArray, getEvenRank } from '@utils/RankMaintenanceUtils'
import { convertFormatToDate } from '@utils/DateUtil'

export interface Props {
  monitoringId: number
  startDate: string
  endDate: string
}

const emptyRankArray: number[] = genEmptyArray(12)

const paddingTableData = (dataArray: KeywordRankMonitoringTablePerDate[], start: string, end: string) => {
  const tableData: KeywordRankMonitoringTablePerDate[] = []
  const startDate = convertFormatToDate(start, DateFnsFormat.DATE)
  const endDate = convertFormatToDate(end, DateFnsFormat.DATE)

  let loopDate = startDate
  while (loopDate <= endDate) {
    const loopDateStr = format(loopDate, DateFnsFormat.DISP_DATE)
    const dateIndex = dataArray.findIndex((item) => item.date === loopDateStr)
    const emptyDateData = {
      date: loopDateStr,
      ranks: emptyRankArray,
      targetRanks: emptyRankArray
    }
    if (dateIndex >= 0) {
      emptyDateData.ranks = getEvenRank(dataArray[dateIndex].ranks)
      emptyDateData.targetRanks = getEvenRank(dataArray[dateIndex].targetRanks)
    }
    tableData.push(emptyDateData)
    loopDate = new Date(loopDate.setDate(loopDate.getDate() + 1))
  }
  return tableData
}

const KeywordRankMonitoringTable: React.FC<Props> = (props: Props): ReactElement => {
  const timeHeaders = [...Array(12).keys()].map((index) => makeHoursLable(index))
  const [tableData, setTableData] = useState<KeywordRankMonitoringTablePerDate[]>([])

  const handleAskTableData = async () => {
    if (props.monitoringId !== 0) {
      const response = await getTableTypeKeywordRankMonitorings({
        monitoringId: props.monitoringId,
        startDate: props.startDate,
        endDate: props.endDate
      })
      setTableData(paddingTableData(response, props.startDate, props.endDate))
    } else {
      setTableData([])
    }
  }

  useEffect(() => {
    handleAskTableData()
  }, [props]) // eslint-disable-line

  return (
    <div id="KeywordRankMonitoringTable">
      <Table data-testid="keywordRankMonitoringTable">
        <colgroup>
          <col className="keywordRankMonitoringTable-column-date" />
          <col className="keywordRankMonitoringTable-column-rank-label" />
          {timeHeaders.map((header, index) => (
            <col className="keywordRankMonitoringTable-column-rank" key={index} />
          ))}
        </colgroup>
        <TableHead data-testid="keywordRankMonitoringTableHeader">
          <TableRow>
            <TableCell>{t('rankMaintenance.label.RankMonitoringModal.tabel.label.date')}</TableCell>
            <TableCell />
            {timeHeaders.map((header, index) => (
              <TableCell
                data-testid={`keywordRankMonitoringTableHeader-${index}`}
                key={`header-${index}`}
                className={`keywordRankMonitoringTable-cell-hour ${
                  index % 6 === 0 ? 'keywordRankMonitoringTable-cell-emphasis' : ''
                }`}
              >
                {header}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody data-testid="keywordRankMonitoringTableBody">
          {tableData.map((item, index) => (
            <Fragment key={`row-${index}`}>
              <TableRow>
                <TableCell
                  data-testid={`keywordRankMonitoringTableBodyDate-${item.date}`}
                  key={`date-${item.date}`}
                  rowSpan={3}
                  className="keywordRankMonitoringTable-cell-date"
                >
                  <div>
                    <span>{item.date.substring(0, 4)}</span>
                    <span>{item.date.substring(5)}</span>
                  </div>
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell
                  data-testid={`keywordRankMonitoringTableBodyDateRank-${item.date}`}
                  key={`dateRank-${item.date}`}
                  className="keywordRankMonitoringTable-cell-rank-label"
                >
                  {t('rankMaintenance.label.RankMonitoringModal.tabel.label.current')}
                </TableCell>
                {item.ranks.map((rank, rankIndex) => (
                  <TableCell
                    data-testid={`keywordRankMonitoringTableBodyDateRank-${item.date}-${rankIndex}`}
                    key={`dateRank-${item.date}-${rankIndex}`}
                    className={`keywordRankMonitoringTable-cell-rank ${
                      rankIndex % 6 === 0 ? 'keywordRankMonitoringTable-cell-separate' : ''
                    }`}
                  >
                    {`${rank ? rank : ''}`}
                  </TableCell>
                ))}
              </TableRow>
              <TableRow>
                <TableCell
                  data-testid={`keywordRankMonitoringTableBodyDateTargetRank-${item.date}`}
                  key={`dateTargetRank-${item.date}`}
                  className="keywordRankMonitoringTable-cell-target-rank-label"
                >
                  {t('rankMaintenance.label.RankMonitoringModal.tabel.label.target')}
                </TableCell>
                {item.targetRanks.map((targetRank, targetRankIndex) => (
                  <TableCell
                    data-testid={`keywordRankMonitoringTableBodyDateTargetRank-${item.date}-${targetRankIndex}`}
                    key={`dateTargetRank-${item.date}-${targetRankIndex}`}
                    className={`keywordRankMonitoringTable-cell-target-rank ${
                      targetRankIndex % 6 === 0 ? 'keywordRankMonitoringTable-cell-separate' : ''
                    }`}
                  >
                    {`${targetRank ? targetRank : ''}`}
                  </TableCell>
                ))}
              </TableRow>
            </Fragment>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

export default KeywordRankMonitoringTable
