import React, { ReactElement, useCallback, useEffect, useState } from 'react'
import { useRecoilState, useRecoilValue } from 'recoil'
import { ExpandLess } from '@material-ui/icons'
import { Box, FormLabel, Grid, MenuItem, OutlinedInput } from '@material-ui/core'
import { useTranslation } from 'react-i18next'
import { TargetBidListQeuryDefault, targetBidListFilterState } from '@store/SsRankMaintenance'
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg'
import { FilterSelectType, pageSizeOptions, SearchingTextLimitLength } from '@models/common/CommonConstants'
import SelectBottom from '@components/common/SelectBottom'
import { GetManualBidListQeury } from '@models/manualBidding'
import { getSsMediaItems, getBidYnItems } from '@utils/CodeUtil'
import '../rankMaintenance/RankMaintenanceListFilter.scss'
import { useAuthority } from '@hooks/common'

const EMPTY_STR = ''
const FILTER_DEFAULT = {
  mediaType: FilterSelectType.ALL,
  adTitleId: EMPTY_STR,
  bidYn: FilterSelectType.ALL
}

const renderDropdownIcon = (props: any) => {
  return (
    <div
      className={`search-input-dropdown ${
        props.className.includes('MuiSelect-iconOpen') ? 'search-input-dropdown-open' : ''
      }`}
    >
      <ExpandLess />
    </div>
  )
}

type SelectItem = { name?: string | undefined; value: unknown }
type SelectEventHandler = (event: React.ChangeEvent<SelectItem>, child: React.ReactNode) => void
interface FilterSelectProps {
  selectId: string //"bidYnSelect"
  itemId: string //bidYbItem
  value: unknown
  onChange: SelectEventHandler
  children?: React.ReactNode
  items: { type: any; name: string }[]
}
const FilterSelect = ({ selectId, itemId, value, onChange, children, items }: FilterSelectProps) => {
  const { t } = useTranslation()

  return (
    <SelectBottom
      data-testid={selectId}
      displayEmpty
      onChange={onChange}
      value={value}
      MenuProps={{
        className: 'rank-maintenance-filter-popover',
        anchorOrigin: {
          vertical: 30,
          horizontal: 'left'
        }
      }}
      IconComponent={(props) => renderDropdownIcon(props)}
    >
      <MenuItem data-testid={`${itemId}-${FilterSelectType.ALL}`} value={FilterSelectType.ALL}>
        {t('common.label.filter.all')}
      </MenuItem>
      {items.map((item) => (
        <MenuItem data-testid={`${itemId}-${item.type}`} key={item.type} value={item.type}>
          {item.name}
        </MenuItem>
      ))}
      {children}
    </SelectBottom>
  )
}

const RankMaintenanceListFilter: React.FC = (): ReactElement => {
  const { t } = useTranslation()
  const { advertiser } = useAuthority()
  const [filterQuery, setFilterQuery] = useRecoilState(targetBidListFilterState)

  const [filter, setFilter] = useState(FILTER_DEFAULT)

  const getFilteredList = useCallback(
    (key: keyof GetManualBidListQeury, value: any, condition: boolean, prevQuery: GetManualBidListQeury) => {
      const query: GetManualBidListQeury = Object.assign({}, prevQuery)
      if (condition) {
        // @ts-ignore
        query[key] = value
      } else {
        delete query[key]
      }
      setFilterQuery(query)
    },
    []
  )

  const handleChange = (event: any, key: keyof typeof FILTER_DEFAULT) => {
    setFilter({ ...filter, [key]: event.target.value })
  }

  const handleSearch = (event: any) => {
    if (event.type === 'keyup' && event.keyCode !== 13) return
    let value = ''

    if (event.keyCode === 13) {
      value = event.target.value.trim()
    }
    if (event.type === 'click') {
      value = event.currentTarget.previousSibling.value.trim()
    }
    const condition = value.length > 0
    getFilteredList('adTitleId', value, condition, filterQuery)
  }

  const changeFilter = (event: any, key: keyof typeof FILTER_DEFAULT) => {
    const value = event.target.value

    const isFiltered = value !== FilterSelectType.ALL
    getFilteredList(key, value, isFiltered, filterQuery)
    setFilter({ ...filter, [key]: event.target.value })
  }

  useEffect(() => {
    //  NOTE: initializeFilter
    setFilterQuery((prev) => ({
      ...prev,
      advertiserId: advertiser?.advertiserId || 1,
      pageSize: pageSizeOptions[0],
      pageIndex: 1
    }))
  }, []) //eslint-disable-line

  return (
    <div id="RankMaintenanceListFilter">
      <Grid container justifyContent="center" className="search-label-container">
        <Grid item>
          <Box className="search-label search-bid-yn">
            <FormLabel>{t('common.label.filter.onoff')}</FormLabel>
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-label search-media">
            <FormLabel>{t('common.label.filter.media')}</FormLabel>
          </Box>
        </Grid>
      </Grid>
      <Grid container className="search-input-container" justifyContent="center">
        <Grid item>
          <Box className="search-input search-bid-yn">
            <FilterSelect
              selectId="bidYnSelect"
              itemId="bidYbItem"
              value={filter.bidYn}
              items={getBidYnItems()}
              onChange={(e) => changeFilter(e, 'bidYn')}
            />
          </Box>
        </Grid>
        <Grid item>
          <Box className="search-input search-media">
            <FilterSelect
              selectId="mediaTypeSelect"
              itemId="mediaTypeItem"
              value={filter.mediaType}
              items={getSsMediaItems()}
              onChange={(e) => changeFilter(e, 'mediaType')}
            />
          </Box>
        </Grid>
      </Grid>
      <Box className="keyword-search-area">
        <div className="search-area">
          <OutlinedInput
            id="outlined-adornment-weight"
            data-testid="keywordNameIdInput"
            value={filter.adTitleId}
            placeholder={t('rankMaintenance.label.RankMaintenanceListPage.placeholder.searchingAd')}
            onChange={(e) => handleChange(e, 'adTitleId')}
            onKeyUp={handleSearch}
            endAdornment={<SearchIcon className="search-icon" onClick={handleSearch} data-testid="searchButton" />}
            aria-describedby="outlined-weight-helper-text"
            labelWidth={0}
            inputProps={{
              maxLength: SearchingTextLimitLength
            }}
          />
        </div>
      </Box>
    </div>
  )
}

export default RankMaintenanceListFilter
