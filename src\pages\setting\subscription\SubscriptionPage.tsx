import { PlanCard } from '@components/subscription/PlanCard'
import React, { useEffect, useState, useRef } from 'react'
import { useLocation, Location as RouterLocation } from 'react-router-dom'
import { createCustomerSession } from '@api/subscription'
import * as SubscriptionModel from '@models/subscription'
import { SubscriptionProductType, SubscriptionViewStatus } from '@models/common/Advertiser'
import CloseIcon from '@material-ui/icons/Close'
import CallMadeIcon from '@material-ui/icons/CallMade'
import { useRecoilValue } from 'recoil'
import { advertiserStateById } from '@store/Advertiser'
import { Dialog, DialogContent, DialogTitle, IconButton } from '@material-ui/core'

interface LocationState {
  advertiserId?: string
  [key: string]: any
}
const capitalizeFirstLetter = (str?: string): string => {
  if (!str) return ''
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

const SubscriptionPage = () => {
  const location = useLocation()
  const state = location.state as LocationState
  const [sessionInfo, setSessionInfo] = useState<SubscriptionModel.CustomerSessionResponse>()
  const formattedProductType = sessionInfo?.productType ? capitalizeFirstLetter(sessionInfo.productType) : ''
  const [open, setOpen] = useState(false)
  const pricingTableRef = useRef<HTMLDivElement>(null)
  const advertiser = useRecoilValue(advertiserStateById(state?.advertiserId || ''))

  useEffect(() => {
    const fetchCustomerSession = async () => {
      if (state && state.advertiserId) {
        try {
          const response = await createCustomerSession(state.advertiserId)
          setSessionInfo(response.data)
        } catch (error) {
          console.error('Error creating customer session:', error)
        }
      } else {
        console.warn('advertiserId not found in state')
      }
    }

    fetchCustomerSession()
  }, [state])

  // Stripe 가격표 스크립트 로드
  useEffect(() => {
    // Dialog가 열린 후 약간의 지연 시간을 두고 DOM이 렌더링되도록 함
    const timer = setTimeout(() => {
      if (open && sessionInfo?.clientSecret && pricingTableRef.current) {
        // 기존 스크립트 제거
        const existingScript = document.querySelector('script[src="https://js.stripe.com/v3/pricing-table.js"]')
        if (existingScript) {
          existingScript.remove()
        }

        // 스크립트 로드
        const script = document.createElement('script')
        script.src = 'https://js.stripe.com/v3/pricing-table.js'
        script.async = true
        document.body.appendChild(script)

        // 스트라이프 API 키 가져오기
        const stripeAPI = JSON.parse(process.env.REACT_APP_STRIPE_API || '{}')
        const pricingTableId = stripeAPI.PRICING_TABLE_ID || ''
        const publishableKey = stripeAPI.PUBLISHABLE_KEY || ''
        // 웹 컴포넌트 생성
        const pricingTableHtml = `
          <stripe-pricing-table
            pricing-table-id="${pricingTableId}"
            publishable-key="${publishableKey}"
            customer-session-client-secret="${sessionInfo.clientSecret || ''}"
          >
          </stripe-pricing-table>
        `
        pricingTableRef.current.innerHTML = pricingTableHtml
      }
    }, 100)

    return () => {
      clearTimeout(timer)
      // 기존 스크립트 제거
      const existingScript = document.querySelector('script[src="https://js.stripe.com/v3/pricing-table.js"]')
      if (existingScript) {
        existingScript.remove()
      }
    }
  }, [open, sessionInfo, state])

  return (
    <div className="p-12">
      <h1 className="text-3xl font-bold">구독 결제 관리</h1>
      <p className="mt-2.5 text-base">현재 이용 중인 플랜 및 구독 내역을 확인하고 변경할 수 있어요.</p>
      <div className="mt-16 flex flex-row gap-7">
        {sessionInfo?.viewStatus && (
          <PlanCard
            variant={sessionInfo?.productType}
            title={advertiser?.advertiserName || ''}
            planName={formattedProductType}
            currentPlan={true}
            displayButton={sessionInfo?.viewStatus === SubscriptionViewStatus.INFO ? false : true}
            period={
              sessionInfo?.subscriptionStartDate && sessionInfo?.subscriptionEndDate
                ? sessionInfo.subscriptionEndDate.startsWith('9999')
                  ? `${sessionInfo.subscriptionStartDate} ~ 이용중`
                  : `${sessionInfo.subscriptionStartDate} ~ ${sessionInfo.subscriptionEndDate}`
                : ''
            }
            buttonLabel={sessionInfo?.productType === SubscriptionProductType.BASIC ? "플랜 구독하기" : "구독 결제 관리"}
            onButtonClick={() => {
              if (sessionInfo?.viewStatus === SubscriptionViewStatus.AVAILABLE) {
                setOpen(true)
              } else if (sessionInfo?.viewStatus === SubscriptionViewStatus.ACTIVE) {
                window.open(sessionInfo.customerPortalUrl, '_blank')
              } else {
                alert('stripe 구독 정보가 없습니다.')
              }
            }}
          />
        )}
        <PlanCard
          title="어떤 플랜이 적합할지 고민되시나요?"
          description="이용 목적에 따라 추천 플랜을 안내드립니다."
          buttonLabel="플랜 자세히보기"
          displayButton={true}
          onButtonClick={() => window.open('https://contents.mop.co.kr/pricing', '_blank')}
        />
      </div>
      <Dialog open={open} maxWidth={'md'} fullWidth={true} onClose={() => setOpen(false)}>
        <DialogTitle style={{ textAlign: 'center', position: 'relative' }}>
          플랜 구독하기
          <IconButton
            aria-label="close"
            onClick={() => setOpen(false)}
            style={{
              position: 'absolute',
              right: 8,
              top: 8
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <div style={{ width: '100%', minHeight: '600px' }} ref={pricingTableRef}></div>
          <div
            onClick={() => window.open('https://contents.mop.co.kr/pricing', '_blank')}
            style={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              justifyContent: 'flex-end'
            }}
          >
            플랜 자세히보기
            <CallMadeIcon style={{ marginLeft: '4px', fontSize: '18px' }} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default SubscriptionPage
