#KeywordRankMonitoringTable {
  .MuiTable-root {
    border-collapse: separate;
    table-layout: fixed;
    border-bottom: 2px solid var(--point_color);

    .keywordRankMonitoringTable-column-date {
      width: 105px;
    }
    .keywordRankMonitoringTable-column-rank-label {
      width: 88px;
    }
  }

  .MuiTableBody-root {
    font-size: 14px;
    font-weight: 400;
    color: var(--point_color);
  }
  .MuiTableCell-body {
    color: var(--point_color);
  }
  .MuiTableCell-root {
    height: 44px;
    padding: 0px;
    border-top: 1px solid var(--point_color);
    text-align: center;
    border-bottom: none;
    box-sizing: border-box;
    &.MuiTableCell-head {
      height: 51px;
      font-size: 12px;
      font-weight: 700;
    }
  }
  .MuiTableRow-root:first-child,
  .MuiTableRow-root:nth-child(2) {
    .MuiTableCell-root {
      border-top: none;
    }
  }

  .MuiTableHead-root {
    position: sticky;
    top: 0px;
    .MuiTableCell-head {
      background-color: var(--point_color);
      color: var(--color-white);
      border-top: none;
      font-size: 14px;
      font-weight: 300;
      &.keywordRankMonitoringTable-cell-emphasis {
        font-weight: 700;
      }
    }
  }

  .MuiTableSortLabel-root {
    .MuiTableSortLabel-icon {
      display: none;
    }

    &.MuiTableSortLabel-active {
      color: var(--point_color);
      .MuiTableSortLabel-icon {
        position: relative;
        display: inline-block;
        width: 12px;
        height: 10px;
        opacity: 1;
        text-indent: -9999px;

        &::after {
          content: '';
          display: inline-block;
          width: 0;
          height: 0;
          position: absolute;
          top: 0;
          right: 0;
          border-style: solid;
          border-width: 10px 6px 0px 6px;
          border-color: var(--point_color) transparent transparent transparent;
        }
      }
    }
  }

  .MuiToolbar-root {
    display: none;
  }
  .keywordRankMonitoringTable-cell-date {
    > div {
      width: 100%;
      display: flex;
      flex-direction: column;
      > span:first-child {
        font-size: 11px;
        font-weight: 500;
      }
      > span:last-child {
        font-size: 24px;
        font-weight: 500;
      }
    }
  }
  .keywordRankMonitoringTable-cell-hour,
  .keywordRankMonitoringTable-cell-rank,
  .keywordRankMonitoringTable-cell-target-rankour {
    // width: 54.7px !important;
  }
  .keywordRankMonitoringTable-cell-target-rank-label,
  .keywordRankMonitoringTable-cell-target-rank {
    background-color: var(--color-white);
  }
  .keywordRankMonitoringTable-cell-rank-label,
  .keywordRankMonitoringTable-cell-target-rank-label {
    border-left: 1px solid var(--point_color);
  }
  .keywordRankMonitoringTable-cell-rank,
  .keywordRankMonitoringTable-cell-target-rank {
    font-size: 18px;
    font-weight: 300;
  }
  .keywordRankMonitoringTable-cell-separate {
    font-weight: 700;
    border-left: 1px solid var(--point_color);
  }
}
