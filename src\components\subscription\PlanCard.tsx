import React from 'react'
import tw from 'twin.macro'
import styled from '@emotion/styled'
import { cn } from '@utils/index'
import { SubscriptionProductType } from '@models/common/Advertiser'

export interface PlanCardProps {
  title: string
  description?: string
  currentPlan?: boolean
  displayButton?: boolean
  planName?: string
  period?: string
  buttonLabel: string
  variant?: SubscriptionProductType
  onButtonClick?: React.ButtonHTMLAttributes<HTMLButtonElement>['onClick']
  className?: string
}

const BaseCard = styled.div<{ variant: SubscriptionProductType }>`
  ${tw`flex flex-col justify-between p-6 bg-white rounded-lg border w-[345px] h-[200px]`}
  ${({ variant }) =>
    variant === SubscriptionProductType.LITE ? tw`border-indigo-400` : variant === SubscriptionProductType.PRO ? tw`border-red-400` : tw`border-[#7E808A]`}
`

export const PlanCard: React.FC<PlanCardProps> = ({
  title,
  description,
  currentPlan = false,
  displayButton = false,
  planName,
  period,
  buttonLabel,
  variant = SubscriptionProductType.BASIC,
  onButtonClick,
  className
}) => {
  const planNameColor = variant === SubscriptionProductType.LITE ? 'text-indigo-600' : variant === SubscriptionProductType.PRO ? 'text-red-600' : 'text-gray-900'
  const actionBtnColor = variant === SubscriptionProductType.LITE ? 'text-indigo-600' : variant === SubscriptionProductType.PRO ? 'text-red-600' : 'text-black'

  return (
    <BaseCard variant={variant} className={cn(className)}>
      <div>
        <h2 className="text-lg font-bold text-gray-900">{title}</h2>
        {description && <p className="mt-2 text-sm text-gray-500">{description}</p>}

        {planName && (
          <>
            {currentPlan && <span className="block text-sm font-medium text-gray-500">현재 이용 중인 플랜</span>}

            <span className={cn('block mt-1 text-2xl font-semibold', planNameColor)}>{planName}</span>

            {period && <p className="mt-1 text-sm text-gray-400">{period}</p>}
          </>
        )}
      </div>
      {}
      {displayButton && (
        <button
          onClick={onButtonClick}
          className={cn('mt-4 inline-flex items-center text-sm font-medium focus:outline-none', actionBtnColor)}
        >
          {buttonLabel}
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            className="ml-1 w-4 h-4"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </button>
      )}
    </BaseCard>
  )
}
