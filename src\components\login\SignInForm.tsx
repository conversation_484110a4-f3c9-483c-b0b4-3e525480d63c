import { useRef, useState } from 'react'
import FloatingInput from '@components/common/mopUI/FloatingInput'
import { ResendEmailType, SignInParams, SignInResponse } from '@models/member/Session'
import { changeMemberStatus, signIn } from '@api/member/Session'
import { TransText } from '@components/common'
import { cn } from '@utils/index'
import CommonResponse, { StatusCode } from '@models/common/CommonResponse'
import { signInSchema } from '@utils/validation/auth'
import { Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { isValidate, isValidateAt, isValidField, isValidObject } from '@utils/validation/utils'
import { useAuthFn } from '@hooks/auth'
import { useSession } from '@hooks/common'
type SignInFormKey = keyof SignInParams
type ValidationErrors = Partial<Record<SignInFormKey, string>>

const SignInForm = () => {
  const authFn = useAuthFn()
  const { updateSession } = useSession()
  const navigate = useNavigate()
  const { t } = useTranslation()
  const [isRequesting, setRequesting] = useState(false)
  const [form, setForm] = useState<SignInParams>({
    email: '',
    password: ''
  })

  const refs: Partial<Record<SignInFormKey, React.RefObject<any>>> = {
    email: useRef<HTMLInputElement>(null),
    password: useRef<HTMLInputElement>(null)
  }
  const changeFormValue = (id: string, value: string | string[]) => {
    setForm((prevForm) => ({
      ...prevForm,
      [id]: value
    }))
  }
  const [errors, setErrors] = useState<ValidationErrors>({})

  function handleAuthResponse({ statusCode, data }: CommonResponse<SignInResponse>) {
    const emailTypeMap: Partial<Record<StatusCode, ResendEmailType>> = {
      [StatusCode.ACCOUNT_DORMANT]: ResendEmailType.VERIFY_DORMANT_MEMBER,
      [StatusCode.ASIS_USER]: ResendEmailType.MIGRATION_MEMBER,
      [StatusCode.WAIT]: ResendEmailType.VERIFY_MEMBER
    }
    switch (statusCode) {
      case StatusCode.ACCOUNT_DORMANT:
      case StatusCode.ASIS_USER:
      case StatusCode.WAIT:
        authFn.routeEmailVerfication(form.email, emailTypeMap[statusCode] as ResendEmailType)
        return
      case StatusCode.PW_EXPIRED:
        if (data) {
          updateSession(data)
          navigate('/update-password', { replace: true })
        }
        return
      case StatusCode.NORMAL:
      case StatusCode.INITIAL:
        if (data) {
          updateSession(data)

          const urlSearchParams = new URLSearchParams(window.location.search)
          const redirectUrl = urlSearchParams.get('redirectUrl')
          if (redirectUrl) {
            window.location.assign(redirectUrl)
          } else {
            // NOTE: navigate('/dashboard') 사용시 intro page로 넘어감
            window.location.assign('/dashboard')
          }
        }
        return
      case StatusCode.ACCOUNT_LOCK:
      case StatusCode.WRONG_EMAIL_OR_PASSWORD:
        authFn.toastResponse(statusCode)
        return
      default:
        authFn.toastResponse('etcFail')
        return
    }
  }

  async function requestSignIn(event: React.FormEvent) {
    event.preventDefault()
    setRequesting(true)
    const result = await isValidate(signInSchema, form)
    if (!isValidObject(result)) {
      const newErrors = Object.fromEntries(
        result.errors.map(({ path, i18nKey, value }) => [
          path,
          t(i18nKey, { value, path: t(`login.label.form.${path}`) })
        ])
      )
      setErrors(newErrors)
      const focusKey = result.errors[0].path as SignInFormKey
      const focusRefs = refs[focusKey]?.current
      if (focusKey && focusRefs) focusRefs.focus()
      setRequesting(false)
      return
    }
    try {
      const response = await signIn(form)
      handleAuthResponse(response)
    } catch (err) {
      console.log(err)
    } finally {
      setRequesting(false)
    }
  }

  const checkValidation = async (e: React.FocusEvent<HTMLInputElement>) => {
    const field = e.target.id as SignInFormKey
    const result = await isValidateAt(signInSchema, field, form)
    let errMsg = ''
    if (!isValidField(result)) {
      const { i18nKey, value, path } = result.error
      errMsg = t(i18nKey, { value, path: t(`login.label.form.${path}`) })
    }
    setErrors((prevErrors) => ({ ...prevErrors, [field]: errMsg }))
  }

  // TODO: remove
  // async function tempChangeStatus(status: string) {
  //   await changeMemberStatus(status)
  // }

  return (
    <>
      <form onSubmit={requestSignIn} className="w-96 space-y-5">
        <FloatingInput
          id="email"
          ref={refs.email}
          label={t('login.label.form.email')}
          value={form.email}
          errMsg={errors.email}
          updateValue={changeFormValue}
          onBlur={checkValidation}
          isRequired
        />
        <FloatingInput
          id="password"
          ref={refs.password}
          label={t('login.label.form.password')}
          value={form.password}
          errMsg={errors.password}
          updateValue={changeFormValue}
          onBlur={checkValidation}
          isRequired
          isPassword
        />
        <TransText
          id="login-button"
          as="button"
          type="submit"
          className={cn(
            'w-full bg-[#171717] text-white border text-center border-[#bbbbbb] rounded px-5 py-3 font-semibold',
            'disabled:bg-opacity-30'
          )}
          disabled={isRequesting}
          i18nKey="login.signin.button"
        />
      </form>
      <div className="my-8 flex items-center justify-center gap-x-4">
        <Link to="/register">
          <TransText as="span" i18nKey="login.label.LoginForm.register" className="pr-2" />
        </Link>
        <Link to="/find-password">
          <TransText as="span" i18nKey="login.label.LoginForm.findPW" className="pr-2" />
        </Link>
      </div>
      <div className="my-8 flex items-center justify-center gap-x-4">
        <TransText
          as="span"
          components={{
            terms: <a href="https://contents.mop.co.kr/terms-of-service"></a>
          }}
          i18nKey="landing.footer.termsOfService"
          className="font-normal text-base"
        />
        <span>|</span>
        <TransText
          as="span"
          components={{
            policy: <a href="https://contents.mop.co.kr/privacy-policy"></a>
          }}
          i18nKey="landing.footer.privacyPolicy"
          className="font-semibold text-lg"
        />
        {/* <span>|</span>
        <Link to="/">
          <TransText
            as="span"
            i18nKey="landing.footer.mop"
            className="font-normal text-base"
          />
        </Link> */}
      </div>
      {/* <div>
        <button onClick={() => tempChangeStatus('PW_EXPIRED')}>PW_EXPIRED</button>
      </div> */}
    </>
  )
}

export default SignInForm
