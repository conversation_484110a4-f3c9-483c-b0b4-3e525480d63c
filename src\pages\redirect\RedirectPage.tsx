import React, { useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { t } from 'i18next'
import Loading from '@components/common/Loading'
import SessionUtil from '@utils/SessionUtil'
import './RedirectPage.scss'

const RedirectPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  useEffect(() => {
    const sessionUtil = new SessionUtil()
    const type = searchParams.get('type')
    const id = searchParams.get('id')

    if (!sessionUtil.getSessionId()) {
      const returnPath = window.location.pathname + window.location.search
      navigate(`/login?redirectUrl=${encodeURIComponent(returnPath)}`, { replace: true })
      return
    }

    if (type === 'subscription' && id) {
      navigate(`/setting/subscription/${id}`, { replace: true })
    } else {
      navigate('/error', { replace: true })
    }
  }, [navigate, searchParams])

  return (
    <div className="redirect-page">
      <div className="loading-container">
        <Loading />
        <div className="loading-text">{t('redirect.label.loading.text')}</div>
      </div>
    </div>
  )
}

export default RedirectPage
