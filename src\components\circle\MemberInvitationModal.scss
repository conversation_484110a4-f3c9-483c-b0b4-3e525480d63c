// FIXME
.member-invitation-modal {
  .MuiDialog-paper {
    padding: 55px 53px;
    width: 1000px;
    height: 546px;
    max-width: unset;
    .MuiDialogContent-root {
      padding: 0px;
    }
  }
  &__header {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 0 0 30px 0;
    border-bottom: 1px solid #dfe0e9;

    .mop-chip {
      color: var(--point_color);
      background-color: var(--bg-gray-light);
      font-size: 10px;
    }

    .mop-icon-box {
      position: absolute;
      top: -30px;
      right: -30px;
    }

    .member-invitation__advertiser {
      font-size: 20px;
      color: var(--color-active-blue);
      font-weight: 700;
    }
  }

  .invitation-table {
    display: grid;
    grid-template-rows: 40px 1fr;
    border-top: 1px solid var(--color-primary);
    border-bottom: 1px solid var(--color-primary);

    &__header,
    &__body {
      display: grid;
      grid-template-columns: 16.6% 16.6% 16.6% 16.6% 16.6% 17%;
      grid-auto-rows: 40px;
      &:empty {
        &::before {
          content: '초대 목록이 없습니다.';
          display: flex;
          justify-content: center;
          align-items: center;
          height: 270px;
          grid-column: span 6;
        }
      }
    }

    &__header {
      grid-template-rows: 40px;
      background-color: var(--bg-gray-light);
      align-items: center;
      justify-items: center;
      font-weight: 700;
      color: var(--point_color);
      font-size: 12px;
      padding-right: 6px;

      > span {
        text-align: center;
      }

      .tooltip-icon {
        margin: 0 2px -2px 0;
        display: inline;
        vertical-align: baseline;
      }
    }

    &__body {
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 8px;
      }
      &::-webkit-scrollbar-thumb {
        border: 2px solid transparent;
        border-radius: 3px;
        background-color: #eeeeee;
      }
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }
      .invitation-table__row {
        display: contents;

        &-cell {
          border-bottom: 1px solid var(--gray-light);
          height: 40px;
          font-size: 12px;
          color: var(--point_color);
          font-weight: 400;
          display: flex;
          align-items: center;
          gap: 4px;

          .mop-icon-box {
            margin: 0 auto;
          }

          .text-icon {
            margin: 0;
          }

          &.Registered,
          &.Invited,
          &.Expired {
            .mop-chip {
              font-weight: 700;
              font-size: 10px;
              width: 100%;
            }
          }

          &.Registered {
            .mop-chip {
              background-color: #e8f2fe;
              --chip-font-color: var(--color-active-blue);
            }
          }
          &.Invited {
            .mop-chip {
              background-color: #e2fcd9;
              --chip-font-color: #47b35d;
            }
          }
          &.Expired {
            .mop-chip {
              background-color: #ffe0db;
              --chip-font-color: #dd4a33;
            }
          }
        }
      }
    }
  }

  .MuiPaper-rounded {
    border-radius: 0px;
  }
}
